"use client";
import { createMinimalMotionComponent } from './create.mjs';

/**
 * HTML components
 */
const MotionA = /*@__PURE__*/ createMinimalMotionComponent("a");
const MotionAbbr = /*@__PURE__*/ createMinimalMotionComponent("abbr");
const MotionAddress = 
/*@__PURE__*/ createMinimalMotionComponent("address");
const MotionArea = /*@__PURE__*/ createMinimalMotionComponent("area");
const MotionArticle = 
/*@__PURE__*/ createMinimalMotionComponent("article");
const MotionAside = /*@__PURE__*/ createMinimalMotionComponent("aside");
const MotionAudio = /*@__PURE__*/ createMinimalMotionComponent("audio");
const MotionB = /*@__PURE__*/ createMinimalMotionComponent("b");
const MotionBase = /*@__PURE__*/ createMinimalMotionComponent("base");
const MotionBdi = /*@__PURE__*/ createMinimalMotionComponent("bdi");
const MotionBdo = /*@__PURE__*/ createMinimalMotionComponent("bdo");
const MotionBig = /*@__PURE__*/ createMinimalMotionComponent("big");
const MotionBlockquote = 
/*@__PURE__*/ createMinimalMotionComponent("blockquote");
const MotionBody = /*@__PURE__*/ createMinimalMotionComponent("body");
const MotionButton = /*@__PURE__*/ createMinimalMotionComponent("button");
const MotionCanvas = /*@__PURE__*/ createMinimalMotionComponent("canvas");
const MotionCaption = 
/*@__PURE__*/ createMinimalMotionComponent("caption");
const MotionCite = /*@__PURE__*/ createMinimalMotionComponent("cite");
const MotionCode = /*@__PURE__*/ createMinimalMotionComponent("code");
const MotionCol = /*@__PURE__*/ createMinimalMotionComponent("col");
const MotionColgroup = 
/*@__PURE__*/ createMinimalMotionComponent("colgroup");
const MotionData = /*@__PURE__*/ createMinimalMotionComponent("data");
const MotionDatalist = 
/*@__PURE__*/ createMinimalMotionComponent("datalist");
const MotionDd = /*@__PURE__*/ createMinimalMotionComponent("dd");
const MotionDel = /*@__PURE__*/ createMinimalMotionComponent("del");
const MotionDetails = 
/*@__PURE__*/ createMinimalMotionComponent("details");
const MotionDfn = /*@__PURE__*/ createMinimalMotionComponent("dfn");
const MotionDialog = /*@__PURE__*/ createMinimalMotionComponent("dialog");
const MotionDiv = /*@__PURE__*/ createMinimalMotionComponent("div");
const MotionDl = /*@__PURE__*/ createMinimalMotionComponent("dl");
const MotionDt = /*@__PURE__*/ createMinimalMotionComponent("dt");
const MotionEm = /*@__PURE__*/ createMinimalMotionComponent("em");
const MotionEmbed = /*@__PURE__*/ createMinimalMotionComponent("embed");
const MotionFieldset = 
/*@__PURE__*/ createMinimalMotionComponent("fieldset");
const MotionFigcaption = 
/*@__PURE__*/ createMinimalMotionComponent("figcaption");
const MotionFigure = /*@__PURE__*/ createMinimalMotionComponent("figure");
const MotionFooter = /*@__PURE__*/ createMinimalMotionComponent("footer");
const MotionForm = /*@__PURE__*/ createMinimalMotionComponent("form");
const MotionH1 = /*@__PURE__*/ createMinimalMotionComponent("h1");
const MotionH2 = /*@__PURE__*/ createMinimalMotionComponent("h2");
const MotionH3 = /*@__PURE__*/ createMinimalMotionComponent("h3");
const MotionH4 = /*@__PURE__*/ createMinimalMotionComponent("h4");
const MotionH5 = /*@__PURE__*/ createMinimalMotionComponent("h5");
const MotionH6 = /*@__PURE__*/ createMinimalMotionComponent("h6");
const MotionHead = /*@__PURE__*/ createMinimalMotionComponent("head");
const MotionHeader = /*@__PURE__*/ createMinimalMotionComponent("header");
const MotionHgroup = /*@__PURE__*/ createMinimalMotionComponent("hgroup");
const MotionHr = /*@__PURE__*/ createMinimalMotionComponent("hr");
const MotionHtml = /*@__PURE__*/ createMinimalMotionComponent("html");
const MotionI = /*@__PURE__*/ createMinimalMotionComponent("i");
const MotionIframe = /*@__PURE__*/ createMinimalMotionComponent("iframe");
const MotionImg = /*@__PURE__*/ createMinimalMotionComponent("img");
const MotionInput = /*@__PURE__*/ createMinimalMotionComponent("input");
const MotionIns = /*@__PURE__*/ createMinimalMotionComponent("ins");
const MotionKbd = /*@__PURE__*/ createMinimalMotionComponent("kbd");
const MotionKeygen = /*@__PURE__*/ createMinimalMotionComponent("keygen");
const MotionLabel = /*@__PURE__*/ createMinimalMotionComponent("label");
const MotionLegend = /*@__PURE__*/ createMinimalMotionComponent("legend");
const MotionLi = /*@__PURE__*/ createMinimalMotionComponent("li");
const MotionLink = /*@__PURE__*/ createMinimalMotionComponent("link");
const MotionMain = /*@__PURE__*/ createMinimalMotionComponent("main");
const MotionMap = /*@__PURE__*/ createMinimalMotionComponent("map");
const MotionMark = /*@__PURE__*/ createMinimalMotionComponent("mark");
const MotionMenu = /*@__PURE__*/ createMinimalMotionComponent("menu");
const MotionMenuitem = 
/*@__PURE__*/ createMinimalMotionComponent("menuitem");
const MotionMeter = /*@__PURE__*/ createMinimalMotionComponent("meter");
const MotionNav = /*@__PURE__*/ createMinimalMotionComponent("nav");
const MotionObject = /*@__PURE__*/ createMinimalMotionComponent("object");
const MotionOl = /*@__PURE__*/ createMinimalMotionComponent("ol");
const MotionOptgroup = 
/*@__PURE__*/ createMinimalMotionComponent("optgroup");
const MotionOption = /*@__PURE__*/ createMinimalMotionComponent("option");
const MotionOutput = /*@__PURE__*/ createMinimalMotionComponent("output");
const MotionP = /*@__PURE__*/ createMinimalMotionComponent("p");
const MotionParam = /*@__PURE__*/ createMinimalMotionComponent("param");
const MotionPicture = 
/*@__PURE__*/ createMinimalMotionComponent("picture");
const MotionPre = /*@__PURE__*/ createMinimalMotionComponent("pre");
const MotionProgress = 
/*@__PURE__*/ createMinimalMotionComponent("progress");
const MotionQ = /*@__PURE__*/ createMinimalMotionComponent("q");
const MotionRp = /*@__PURE__*/ createMinimalMotionComponent("rp");
const MotionRt = /*@__PURE__*/ createMinimalMotionComponent("rt");
const MotionRuby = /*@__PURE__*/ createMinimalMotionComponent("ruby");
const MotionS = /*@__PURE__*/ createMinimalMotionComponent("s");
const MotionSamp = /*@__PURE__*/ createMinimalMotionComponent("samp");
const MotionScript = /*@__PURE__*/ createMinimalMotionComponent("script");
const MotionSection = 
/*@__PURE__*/ createMinimalMotionComponent("section");
const MotionSelect = /*@__PURE__*/ createMinimalMotionComponent("select");
const MotionSmall = /*@__PURE__*/ createMinimalMotionComponent("small");
const MotionSource = /*@__PURE__*/ createMinimalMotionComponent("source");
const MotionSpan = /*@__PURE__*/ createMinimalMotionComponent("span");
const MotionStrong = /*@__PURE__*/ createMinimalMotionComponent("strong");
const MotionStyle = /*@__PURE__*/ createMinimalMotionComponent("style");
const MotionSub = /*@__PURE__*/ createMinimalMotionComponent("sub");
const MotionSummary = 
/*@__PURE__*/ createMinimalMotionComponent("summary");
const MotionSup = /*@__PURE__*/ createMinimalMotionComponent("sup");
const MotionTable = /*@__PURE__*/ createMinimalMotionComponent("table");
const MotionTbody = /*@__PURE__*/ createMinimalMotionComponent("tbody");
const MotionTd = /*@__PURE__*/ createMinimalMotionComponent("td");
const MotionTextarea = 
/*@__PURE__*/ createMinimalMotionComponent("textarea");
const MotionTfoot = /*@__PURE__*/ createMinimalMotionComponent("tfoot");
const MotionTh = /*@__PURE__*/ createMinimalMotionComponent("th");
const MotionThead = /*@__PURE__*/ createMinimalMotionComponent("thead");
const MotionTime = /*@__PURE__*/ createMinimalMotionComponent("time");
const MotionTitle = /*@__PURE__*/ createMinimalMotionComponent("title");
const MotionTr = /*@__PURE__*/ createMinimalMotionComponent("tr");
const MotionTrack = /*@__PURE__*/ createMinimalMotionComponent("track");
const MotionU = /*@__PURE__*/ createMinimalMotionComponent("u");
const MotionUl = /*@__PURE__*/ createMinimalMotionComponent("ul");
const MotionVideo = /*@__PURE__*/ createMinimalMotionComponent("video");
const MotionWbr = /*@__PURE__*/ createMinimalMotionComponent("wbr");
const MotionWebview = 
/*@__PURE__*/ createMinimalMotionComponent("webview");
/**
 * SVG components
 */
const MotionAnimate = 
/*@__PURE__*/ createMinimalMotionComponent("animate");
const MotionCircle = /*@__PURE__*/ createMinimalMotionComponent("circle");
const MotionDefs = /*@__PURE__*/ createMinimalMotionComponent("defs");
const MotionDesc = /*@__PURE__*/ createMinimalMotionComponent("desc");
const MotionEllipse = 
/*@__PURE__*/ createMinimalMotionComponent("ellipse");
const MotionG = /*@__PURE__*/ createMinimalMotionComponent("g");
const MotionImage = /*@__PURE__*/ createMinimalMotionComponent("image");
const MotionLine = /*@__PURE__*/ createMinimalMotionComponent("line");
const MotionFilter = /*@__PURE__*/ createMinimalMotionComponent("filter");
const MotionMarker = /*@__PURE__*/ createMinimalMotionComponent("marker");
const MotionMask = /*@__PURE__*/ createMinimalMotionComponent("mask");
const MotionMetadata = 
/*@__PURE__*/ createMinimalMotionComponent("metadata");
const MotionPath = /*@__PURE__*/ createMinimalMotionComponent("path");
const MotionPattern = 
/*@__PURE__*/ createMinimalMotionComponent("pattern");
const MotionPolygon = 
/*@__PURE__*/ createMinimalMotionComponent("polygon");
const MotionPolyline = 
/*@__PURE__*/ createMinimalMotionComponent("polyline");
const MotionRect = /*@__PURE__*/ createMinimalMotionComponent("rect");
const MotionStop = /*@__PURE__*/ createMinimalMotionComponent("stop");
const MotionSvg = /*@__PURE__*/ createMinimalMotionComponent("svg");
const MotionSymbol = /*@__PURE__*/ createMinimalMotionComponent("symbol");
const MotionText = /*@__PURE__*/ createMinimalMotionComponent("text");
const MotionTspan = /*@__PURE__*/ createMinimalMotionComponent("tspan");
const MotionUse = /*@__PURE__*/ createMinimalMotionComponent("use");
const MotionView = /*@__PURE__*/ createMinimalMotionComponent("view");
const MotionClipPath = 
/*@__PURE__*/ createMinimalMotionComponent("clipPath");
const MotionFeBlend = 
/*@__PURE__*/ createMinimalMotionComponent("feBlend");
const MotionFeColorMatrix = 
/*@__PURE__*/ createMinimalMotionComponent("feColorMatrix");
const MotionFeComponentTransfer = 
/*@__PURE__*/ createMinimalMotionComponent("feComponentTransfer");
const MotionFeComposite = 
/*@__PURE__*/ createMinimalMotionComponent("feComposite");
const MotionFeConvolveMatrix = 
/*@__PURE__*/ createMinimalMotionComponent("feConvolveMatrix");
const MotionFeDiffuseLighting = 
/*@__PURE__*/ createMinimalMotionComponent("feDiffuseLighting");
const MotionFeDisplacementMap = 
/*@__PURE__*/ createMinimalMotionComponent("feDisplacementMap");
const MotionFeDistantLight = 
/*@__PURE__*/ createMinimalMotionComponent("feDistantLight");
const MotionFeDropShadow = 
/*@__PURE__*/ createMinimalMotionComponent("feDropShadow");
const MotionFeFlood = 
/*@__PURE__*/ createMinimalMotionComponent("feFlood");
const MotionFeFuncA = 
/*@__PURE__*/ createMinimalMotionComponent("feFuncA");
const MotionFeFuncB = 
/*@__PURE__*/ createMinimalMotionComponent("feFuncB");
const MotionFeFuncG = 
/*@__PURE__*/ createMinimalMotionComponent("feFuncG");
const MotionFeFuncR = 
/*@__PURE__*/ createMinimalMotionComponent("feFuncR");
const MotionFeGaussianBlur = 
/*@__PURE__*/ createMinimalMotionComponent("feGaussianBlur");
const MotionFeImage = 
/*@__PURE__*/ createMinimalMotionComponent("feImage");
const MotionFeMerge = 
/*@__PURE__*/ createMinimalMotionComponent("feMerge");
const MotionFeMergeNode = 
/*@__PURE__*/ createMinimalMotionComponent("feMergeNode");
const MotionFeMorphology = 
/*@__PURE__*/ createMinimalMotionComponent("feMorphology");
const MotionFeOffset = 
/*@__PURE__*/ createMinimalMotionComponent("feOffset");
const MotionFePointLight = 
/*@__PURE__*/ createMinimalMotionComponent("fePointLight");
const MotionFeSpecularLighting = 
/*@__PURE__*/ createMinimalMotionComponent("feSpecularLighting");
const MotionFeSpotLight = 
/*@__PURE__*/ createMinimalMotionComponent("feSpotLight");
const MotionFeTile = /*@__PURE__*/ createMinimalMotionComponent("feTile");
const MotionFeTurbulence = 
/*@__PURE__*/ createMinimalMotionComponent("feTurbulence");
const MotionForeignObject = 
/*@__PURE__*/ createMinimalMotionComponent("foreignObject");
const MotionLinearGradient = 
/*@__PURE__*/ createMinimalMotionComponent("linearGradient");
const MotionRadialGradient = 
/*@__PURE__*/ createMinimalMotionComponent("radialGradient");
const MotionTextPath = 
/*@__PURE__*/ createMinimalMotionComponent("textPath");

export { MotionA, MotionAbbr, MotionAddress, MotionAnimate, MotionArea, MotionArticle, MotionAside, MotionAudio, MotionB, MotionBase, MotionBdi, MotionBdo, MotionBig, MotionBlockquote, MotionBody, MotionButton, MotionCanvas, MotionCaption, MotionCircle, MotionCite, MotionClipPath, MotionCode, MotionCol, MotionColgroup, MotionData, MotionDatalist, MotionDd, MotionDefs, MotionDel, MotionDesc, MotionDetails, MotionDfn, MotionDialog, MotionDiv, MotionDl, MotionDt, MotionEllipse, MotionEm, MotionEmbed, MotionFeBlend, MotionFeColorMatrix, MotionFeComponentTransfer, MotionFeComposite, MotionFeConvolveMatrix, MotionFeDiffuseLighting, MotionFeDisplacementMap, MotionFeDistantLight, MotionFeDropShadow, MotionFeFlood, MotionFeFuncA, MotionFeFuncB, MotionFeFuncG, MotionFeFuncR, MotionFeGaussianBlur, MotionFeImage, MotionFeMerge, MotionFeMergeNode, MotionFeMorphology, MotionFeOffset, MotionFePointLight, MotionFeSpecularLighting, MotionFeSpotLight, MotionFeTile, MotionFeTurbulence, MotionFieldset, MotionFigcaption, MotionFigure, MotionFilter, MotionFooter, MotionForeignObject, MotionForm, MotionG, MotionH1, MotionH2, MotionH3, MotionH4, MotionH5, MotionH6, MotionHead, MotionHeader, MotionHgroup, MotionHr, MotionHtml, MotionI, MotionIframe, MotionImage, MotionImg, MotionInput, MotionIns, MotionKbd, MotionKeygen, MotionLabel, MotionLegend, MotionLi, MotionLine, MotionLinearGradient, MotionLink, MotionMain, MotionMap, MotionMark, MotionMarker, MotionMask, MotionMenu, MotionMenuitem, MotionMetadata, MotionMeter, MotionNav, MotionObject, MotionOl, MotionOptgroup, MotionOption, MotionOutput, MotionP, MotionParam, MotionPath, MotionPattern, MotionPicture, MotionPolygon, MotionPolyline, MotionPre, MotionProgress, MotionQ, MotionRadialGradient, MotionRect, MotionRp, MotionRt, MotionRuby, MotionS, MotionSamp, MotionScript, MotionSection, MotionSelect, MotionSmall, MotionSource, MotionSpan, MotionStop, MotionStrong, MotionStyle, MotionSub, MotionSummary, MotionSup, MotionSvg, MotionSymbol, MotionTable, MotionTbody, MotionTd, MotionText, MotionTextPath, MotionTextarea, MotionTfoot, MotionTh, MotionThead, MotionTime, MotionTitle, MotionTr, MotionTrack, MotionTspan, MotionU, MotionUl, MotionUse, MotionVideo, MotionView, MotionWbr, MotionWebview };
