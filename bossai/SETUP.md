# 🚀 Boss AI Investment Platform - Setup Guide

## 🎯 Quick Start

### 1. **Database Setup (Required)**

You need a PostgreSQL database. Choose one option:

#### Option A: Local PostgreSQL
```bash
# Install PostgreSQL locally
brew install postgresql  # macOS
# or use Docker
docker run --name bossai-postgres -e POSTGRES_PASSWORD=password -e POSTGRES_DB=bossai_db -p 5432:5432 -d postgres
```

#### Option B: Cloud Database (Recommended)
- **Supabase**: https://supabase.com (Free tier available)
- **Neon**: https://neon.tech (Free tier available)
- **PlanetScale**: https://planetscale.com

### 2. **Environment Configuration**

Update `.env.local` with your database URL:

```bash
# Replace with your actual database URL
DATABASE_URL="postgresql://username:password@localhost:5432/bossai_db?schema=public"
DIRECT_URL="postgresql://username:password@localhost:5432/bossai_db?schema=public"
```

### 3. **Database Setup**

```bash
# Generate Prisma client
npm run db:generate

# Push database schema
npm run db:push

# Seed with test users
npm run db:seed
```

### 4. **Start Development Server**

```bash
npm run dev
```

Visit: http://localhost:3000

## 🔐 Test Login Credentials

After running `npm run db:seed`, you can use these test accounts:

### Admin Account
- **Email**: `<EMAIL>`
- **Password**: `BossAI2024!`
- **Access**: Full admin dashboard, user management, investment approvals

### Investor Account  
- **Email**: `<EMAIL>`
- **Password**: `Investor2024!`
- **Access**: Investment dashboard, KYC forms, portfolio tracking

## 🔧 Google OAuth Setup (Optional but Recommended)

1. Go to [Google Cloud Console](https://console.developers.google.com/)
2. Create a new project or select existing
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URI: `http://localhost:3000/api/auth/callback/google`
6. Update `.env.local`:

```bash
GOOGLE_CLIENT_ID=your-google-client-id
GOOGLE_CLIENT_SECRET=your-google-client-secret
```

## 📋 Platform Features

### ✅ **Authentication System**
- Email/password signup and signin
- Google OAuth integration
- Role-based access control (USER, INVESTOR, ADMIN, SUPER_ADMIN)
- Secure session management

### ✅ **Investment Dashboard**
- Portfolio overview and statistics
- Investment history tracking
- KYC/AML status monitoring
- Document management

### ✅ **Investment Forms**
- Comprehensive investment application
- Multi-step form with validation
- Risk assessment and compliance checks
- Document upload capabilities

### ✅ **Admin Portal**
- User management and role assignment
- Investment review and approval
- KYC/AML verification workflow
- Audit trail and compliance monitoring

### ✅ **Legal Compliance**
- Terms of Service
- Privacy Policy
- Cookie Policy
- KYC/AML forms
- Audit logging

## 🛡️ Security Features

- **Password Security**: Bcrypt hashing with 12 rounds
- **Session Management**: Secure JWT tokens with NextAuth.js
- **CSRF Protection**: Built-in CSRF token validation
- **Rate Limiting**: API endpoint protection
- **Data Encryption**: Environment-based encryption keys
- **Audit Logging**: Complete user action tracking

## 📁 Project Structure

```
bossai/
├── src/
│   ├── app/                    # Next.js App Router
│   │   ├── api/               # API endpoints
│   │   ├── auth/              # Authentication pages
│   │   ├── dashboard/         # User dashboard
│   │   ├── admin/             # Admin portal
│   │   ├── boss-ai-investments/ # Investment forms
│   │   └── legal/             # Legal pages
│   ├── components/            # React components
│   │   ├── auth/              # Authentication components
│   │   ├── dashboard/         # Dashboard components
│   │   ├── investment/        # Investment forms
│   │   ├── kyc/               # KYC/AML components
│   │   └── ui/                # UI components
│   ├── lib/                   # Utilities and configurations
│   └── types/                 # TypeScript type definitions
├── prisma/                    # Database schema and migrations
├── scripts/                   # Database seeding scripts
└── public/                    # Static assets
```

## 🚀 Deployment

### Vercel (Recommended)
1. Connect your GitHub repository to Vercel
2. Add environment variables in Vercel dashboard
3. Deploy automatically on push

### Environment Variables for Production
```bash
# Database
DATABASE_URL="your-production-database-url"
DIRECT_URL="your-production-database-url"

# NextAuth
NEXTAUTH_URL="https://your-domain.com"
NEXTAUTH_SECRET="your-production-secret"

# Google OAuth
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"

# Email (for notifications)
EMAIL_SERVER_HOST="smtp.gmail.com"
EMAIL_SERVER_PORT=587
EMAIL_SERVER_USER="<EMAIL>"
EMAIL_SERVER_PASSWORD="your-app-password"
```

## 🔍 Troubleshooting

### Database Connection Issues
```bash
# Check if database is running
psql -h localhost -p 5432 -U username -d bossai_db

# Reset database
npm run db:push --force-reset
npm run db:seed
```

### Authentication Issues
- Ensure `NEXTAUTH_SECRET` is set
- Check Google OAuth credentials
- Verify redirect URLs match exactly

### Build Issues
```bash
# Clear Next.js cache
rm -rf .next
npm run build
```

## 📞 Support

For technical support or questions:
- Email: <EMAIL>
- Documentation: [Internal Wiki]
- Issues: Create GitHub issue

## 🎉 You're Ready!

Your Boss AI Investment Platform is now ready for:
- ✅ User registration and authentication
- ✅ Investment form submissions
- ✅ KYC/AML compliance workflows
- ✅ Admin management and oversight
- ✅ Legal compliance and audit trails

**Next Steps:**
1. Customize branding and content
2. Configure email notifications
3. Set up payment processing (Stripe)
4. Add real investment opportunities
5. Deploy to production
