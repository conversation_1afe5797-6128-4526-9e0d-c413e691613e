# AI Venture Platform

A next-generation AI venture platform built with modern web technologies, featuring enterprise-grade engineering and immersive design.

## 🚀 Features

- **Modern Tech Stack**: Next.js 14+, TypeScript, React, TailwindCSS
- **AI-Inspired Design**: Deep space color palette with futuristic aesthetics
- **Smooth Animations**: Framer Motion for micro-interactions and page transitions
- **Responsive Design**: Mobile-first approach with seamless desktop experience
- **Dark/Light Theme**: Built-in theme switching with next-themes
- **Component Architecture**: Modular, reusable components with shadcn/ui
- **SEO Optimized**: Structured data, meta tags, and performance optimizations
- **Accessibility**: WCAG compliant with proper ARIA labels and keyboard navigation

## 🎨 Design System

### Color Palette
- **Primary**: Deep Space Blue (#0f172a)
- **Accent 1**: Electric Blue (#3b82f6)
- **Accent 2**: Quantum Purple (#8b5cf6)
- **Highlight**: Cyber Green (#22c55e)
- **Neutral**: Charcoal Black (#0a0a0a) & Cloud Gray (#9ca3af)

### Typography
- **Display**: Inter Black/Bold for headlines
- **Body**: Inter for clean, readable text

### Visual Effects
- Floating orb animations
- Neural network patterns
- Gradient text effects
- Interactive card hover states
- Smooth page transitions

## 🛠 Tech Stack

### Frontend
- **Next.js 15.5.2** - React framework with App Router
- **TypeScript** - Type safety and developer experience
- **TailwindCSS v4** - Utility-first CSS framework
- **Framer Motion** - Animation library
- **shadcn/ui** - High-quality UI components
- **next-themes** - Theme management
- **Lucide React** - Icon library

### Development Tools
- **ESLint** - Code linting
- **PostCSS** - CSS processing
- **Turbopack** - Fast bundler

## 🚀 Getting Started

### Prerequisites
- Node.js 18+
- npm or yarn

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd bossai
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Run the development server**
   ```bash
   npm run dev
   ```

4. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

### Build for Production

```bash
npm run build
npm start
```
