import { PrismaClient, UserRole } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Create test admin user
  const adminPassword = await bcrypt.hash('admin123', 12);
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Boss AI Admin',
      role: UserRole.ADMIN,
      password: adminPassword,
      emailVerified: new Date(),
    },
  });

  // Create test investor user
  const investorPassword = await bcrypt.hash('investor123', 12);
  const investor = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      email: '<EMAIL>',
      name: 'Test Investor',
      role: UserRole.INVESTOR,
      password: investorPassword,
      emailVerified: new Date(),
    },
  });

  // Create a sample project
  const project = await prisma.project.upsert({
    where: { id: 'sample-project-1' },
    update: {},
    create: {
      id: 'sample-project-1',
      title: 'AI-Powered Healthcare Platform',
      description: 'Revolutionary AI platform for healthcare diagnostics and patient management.',
      sector: 'Healthcare',
      fundingGoal: 1000000,
      currentFunding: 250000,
      roiEstimate: 25.5,
      status: 'ACTIVE',
      featured: true,
      minInvestment: 10000,
      maxInvestment: 100000,
      riskLevel: 'MODERATE',
      timeHorizon: '3-5 years',
      startDate: new Date(),
      imageUrl: '/images/healthcare-ai.jpg',
      documentUrls: 'business-plan.pdf,financial-projections.xlsx',
      tags: 'AI,Healthcare,Technology',
      creatorId: admin.id,
    },
  });

  // Create another sample project
  const project2 = await prisma.project.upsert({
    where: { id: 'sample-project-2' },
    update: {},
    create: {
      id: 'sample-project-2',
      title: 'Green Energy Storage Solutions',
      description: 'Next-generation battery technology for renewable energy storage.',
      sector: 'Clean Energy',
      fundingGoal: 2000000,
      currentFunding: 500000,
      roiEstimate: 30.0,
      status: 'ACTIVE',
      featured: true,
      minInvestment: 25000,
      maxInvestment: 250000,
      riskLevel: 'AGGRESSIVE',
      timeHorizon: '5-7 years',
      startDate: new Date(),
      imageUrl: '/images/green-energy.jpg',
      documentUrls: 'tech-specs.pdf,market-analysis.pdf',
      tags: 'Clean Energy,Technology,Sustainability',
      creatorId: admin.id,
    },
  });

  console.log('✅ Database seeded successfully!');
  console.log('📧 Test Accounts Created:');
  console.log('   Admin: <EMAIL> / admin123');
  console.log('   Investor: <EMAIL> / investor123');
  console.log('🚀 Projects Created:', project.title, 'and', project2.title);
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
