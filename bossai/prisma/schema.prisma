// Boss AI Platform Database Schema
// Full-stack investment + AI venture platform

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User Management & Authentication
model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String?
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@index([userId])
  @@index([provider])
  @@map("accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@index([userId])
  @@index([expires])
  @@map("sessions")
}

model User {
  id            String    @id @default(cuid())
  name          String?
  email         String    @unique
  emailVerified DateTime?
  image         String?
  password      String?   // For credentials login
  role          UserRole  @default(USER)
  status        UserStatus @default(PENDING)

  // KYC and Profile Information
  firstName     String?
  lastName      String?
  phone         String?   @unique
  dateOfBirth   DateTime?
  address       String?
  city          String?
  state         String?
  zipCode       String?
  country       String?

  // Investment Profile
  riskTolerance RiskLevel?
  investmentExperience String?
  annualIncome  String?
  netWorth      String?

  // Platform Data
  kycStatus     KYCStatus @default(NOT_STARTED)
  kycCompletedAt DateTime?
  lastLoginAt   DateTime?
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt

  // Relations
  accounts        Account[]
  sessions        Session[]
  investments     Investment[]
  documents       Document[]
  notes           Note[]
  auditLogs       AuditLog[]
  notifications   Notification[]
  investmentForms InvestmentForm[]
  createdProjects Project[]       @relation("ProjectCreator")

  @@index([email])
  @@index([role])
  @@index([status])
  @@index([kycStatus])
  @@index([createdAt])
  @@index([lastLoginAt])
  @@index([phone])
  @@map("users")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map("verificationtokens")
}

// Investment Management
model Investment {
  id              String          @id @default(cuid())
  userId          String
  type            InvestmentType
  amount          Decimal         @db.Decimal(15, 2)
  currency        String          @default("USD")
  status          InvestmentStatus @default(PENDING)

  // Investment Details
  description     String?
  terms           String?
  expectedReturn  Decimal?        @db.Decimal(5, 2)
  duration        Int?            // Duration in months
  riskLevel       RiskLevel

  // Processing Information
  submittedAt     DateTime        @default(now())
  approvedAt      DateTime?
  rejectedAt      DateTime?
  completedAt     DateTime?

  // Financial Tracking
  currentValue    Decimal?        @db.Decimal(15, 2)
  totalReturns    Decimal?        @db.Decimal(15, 2)

  // Relations
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  projectId       String?
  project         Project?        @relation(fields: [projectId], references: [id])
  documents       Document[]
  transactions    Transaction[]
  notes           Note[]

  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  @@index([userId])
  @@index([status])
  @@index([type])
  @@index([projectId])
  @@index([submittedAt])
  @@index([createdAt])
  @@index([userId, status])
  @@index([projectId, status])
  @@map("investments")
}

// Document Management
model Document {
  id              String          @id @default(cuid())
  userId          String
  investmentId    String?

  // File Information
  fileName        String
  originalName    String
  filePath        String
  fileSize        Int
  mimeType        String

  // Document Classification
  type            DocumentType
  category        DocumentCategory
  status          DocumentStatus  @default(PENDING)

  // Verification
  verifiedAt      DateTime?
  verifiedBy      String?
  rejectionReason String?

  // Metadata
  uploadedAt      DateTime        @default(now())
  expiresAt       DateTime?

  // Relations
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  investment      Investment?     @relation(fields: [investmentId], references: [id], onDelete: SetNull)
  investmentFormId String?
  investmentForm  InvestmentForm? @relation(fields: [investmentFormId], references: [id], onDelete: SetNull)

  @@index([userId])
  @@index([investmentId])
  @@index([type])
  @@index([category])
  @@index([status])
  @@index([uploadedAt])
  @@index([expiresAt])
  @@index([userId, type])
  @@index([userId, status])
  @@map("documents")
}

// Transaction History
model Transaction {
  id              String          @id @default(cuid())
  investmentId    String

  // Transaction Details
  type            TransactionType
  amount          Decimal         @db.Decimal(15, 2)
  currency        String          @default("USD")
  status          TransactionStatus @default(PENDING)

  // Payment Information
  paymentMethod   String?
  paymentId       String?         // External payment processor ID

  // Processing
  processedAt     DateTime?
  failedAt        DateTime?
  failureReason   String?

  // Relations
  investment      Investment      @relation(fields: [investmentId], references: [id], onDelete: Cascade)

  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  @@index([investmentId])
  @@index([type])
  @@index([status])
  @@index([createdAt])
  @@index([processedAt])
  @@index([paymentId])
  @@index([investmentId, type])
  @@index([investmentId, status])
  @@map("transactions")
}

// Notes and Communication
model Note {
  id              String          @id @default(cuid())
  userId          String
  investmentId    String?

  // Note Content
  title           String?
  content         String
  type            NoteType        @default(GENERAL)
  priority        Priority        @default(NORMAL)

  // Visibility and Access
  isPrivate       Boolean         @default(false)
  createdBy       String          // Admin user ID who created the note

  // Relations
  user            User            @relation(fields: [userId], references: [id], onDelete: Cascade)
  investment      Investment?     @relation(fields: [investmentId], references: [id], onDelete: SetNull)

  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  @@index([userId])
  @@index([investmentId])
  @@index([type])
  @@index([priority])
  @@index([createdBy])
  @@index([createdAt])
  @@index([userId, type])
  @@map("notes")
}

// Audit Trail
model AuditLog {
  id              String          @id @default(cuid())
  userId          String?

  // Action Details
  action          String
  entity          String          // Table/model name
  entityId        String          // Record ID

  // Change Tracking
  oldValues       Json?
  newValues       Json?

  // Context
  ipAddress       String?
  userAgent       String?
  sessionId       String?

  // Relations
  user            User?           @relation(fields: [userId], references: [id], onDelete: SetNull)

  createdAt       DateTime        @default(now())

  @@index([userId])
  @@index([action])
  @@index([entity])
  @@index([entityId])
  @@index([createdAt])
  @@index([sessionId])
  @@index([entity, entityId])
  @@index([userId, action])
  @@map("audit_logs")
}

// Project Management
model Project {
  id              String          @id @default(cuid())
  title           String
  description     String
  sector          String
  fundingGoal     Decimal         @db.Decimal(15, 2)
  currentFunding  Decimal         @default(0) @db.Decimal(15, 2)
  roiEstimate     Decimal?        @db.Decimal(5, 2)
  status          ProjectStatus   @default(DRAFT)
  featured        Boolean         @default(false)
  minInvestment   Decimal         @db.Decimal(15, 2)
  maxInvestment   Decimal?        @db.Decimal(15, 2)
  riskLevel       RiskLevel       @default(MODERATE)
  timeHorizon     String?
  startDate       DateTime?
  endDate         DateTime?
  completionDate  DateTime?
  imageUrl        String?
  documentUrls    String?
  tags            String?

  // Relations
  creatorId       String
  creator         User            @relation("ProjectCreator", fields: [creatorId], references: [id])
  investments     Investment[]
  investmentForms InvestmentForm[]

  createdAt       DateTime        @default(now())
  updatedAt       DateTime        @updatedAt

  @@index([status])
  @@index([sector])
  @@index([featured])
  @@index([creatorId])
  @@index([createdAt])
  @@index([riskLevel])
  @@index([status, featured])
  @@index([sector, status])
  @@map("projects")
}

// Investment Form Submissions
model InvestmentForm {
  id                    String              @id @default(cuid())

  // Personal Information
  firstName             String
  lastName              String
  email                 String
  phone                 String
  dateOfBirth           DateTime

  // Investment Details
  projectId             String
  project               Project             @relation(fields: [projectId], references: [id])
  investmentAmount      Decimal             @db.Decimal(15, 2)
  investmentType        String
  riskTolerance         String
  timeHorizon           String
  investmentGoals       String?

  // Financial Information
  annualIncome          String
  netWorth              String
  liquidAssets          String?
  investmentExperience  String

  // Legal and Compliance
  accreditedInvestor    Boolean             @default(false)
  agreeToTerms          Boolean             @default(false)
  agreeToPrivacy        Boolean             @default(false)

  // Form Status
  status                FormStatus          @default(PENDING)
  reviewedAt            DateTime?
  reviewedBy            String?
  reviewNotes           String?

  // Relations
  userId                String
  user                  User                @relation(fields: [userId], references: [id])
  documents             Document[]

  createdAt             DateTime            @default(now())
  updatedAt             DateTime            @updatedAt

  @@index([userId])
  @@index([projectId])
  @@index([status])
  @@index([email])
  @@index([createdAt])
  @@index([reviewedAt])
  @@index([userId, status])
  @@index([projectId, status])
  @@map("investment_forms")
}

// Notifications System
model Notification {
  id          String              @id @default(cuid())
  title       String
  message     String
  type        NotificationType
  priority    Priority            @default(NORMAL)
  read        Boolean             @default(false)
  actionUrl   String?

  // Relations
  userId      String
  user        User                @relation(fields: [userId], references: [id])

  createdAt   DateTime            @default(now())
  updatedAt   DateTime            @updatedAt

  @@index([userId])
  @@index([read])
  @@index([type])
  @@index([priority])
  @@index([createdAt])
  @@index([userId, read])
  @@index([userId, type])
  @@map("notifications")
}

// Enums
enum UserRole {
  USER
  INVESTOR
  ADMIN
  SUPER_ADMIN
}

enum UserStatus {
  PENDING
  ACTIVE
  SUSPENDED
  DEACTIVATED
}

enum KYCStatus {
  NOT_STARTED
  IN_PROGRESS
  PENDING_REVIEW
  APPROVED
  REJECTED
  EXPIRED
}

enum RiskLevel {
  CONSERVATIVE
  MODERATE
  AGGRESSIVE
  VERY_AGGRESSIVE
}

enum InvestmentType {
  EQUITY
  DEBT
  HYBRID
  REAL_ESTATE
  CRYPTOCURRENCY
  COMMODITIES
  OTHER
}

enum InvestmentStatus {
  PENDING
  UNDER_REVIEW
  APPROVED
  REJECTED
  ACTIVE
  COMPLETED
  CANCELLED
}

enum DocumentType {
  IDENTITY
  PROOF_OF_ADDRESS
  INCOME_VERIFICATION
  BANK_STATEMENT
  TAX_RETURN
  INVESTMENT_AGREEMENT
  KYC_FORM
  OTHER
}

enum DocumentCategory {
  KYC
  LEGAL
  FINANCIAL
  COMPLIANCE
  INVESTMENT
}

enum DocumentStatus {
  PENDING
  UNDER_REVIEW
  APPROVED
  REJECTED
  EXPIRED
}

enum TransactionType {
  DEPOSIT
  WITHDRAWAL
  INVESTMENT
  RETURN
  FEE
  REFUND
}

enum TransactionStatus {
  PENDING
  PROCESSING
  COMPLETED
  FAILED
  CANCELLED
}

enum NoteType {
  GENERAL
  KYC
  INVESTMENT
  COMPLIANCE
  SUPPORT
  INTERNAL
}

enum Priority {
  LOW
  NORMAL
  HIGH
  URGENT
}

enum ProjectStatus {
  DRAFT
  ACTIVE
  FUNDING
  FUNDED
  COMPLETED
  CANCELLED
  ARCHIVED
}

enum FormStatus {
  PENDING
  UNDER_REVIEW
  APPROVED
  REJECTED
  CANCELLED
}

enum NotificationType {
  INFO
  SUCCESS
  WARNING
  ERROR
  INVESTMENT_APPROVED
  INVESTMENT_REJECTED
  KYC_APPROVED
  KYC_REJECTED
  PROJECT_UPDATE
  FORM_SUBMISSION
  SYSTEM_ALERT
}
