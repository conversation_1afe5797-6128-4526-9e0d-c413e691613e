import { 
  HeroData, 
  StoryData, 
  WhatWeDoData, 
  TomorrowData, 
  PartnershipData, 
  TechTrustData, 
  CTAData 
} from '@/types';

export const heroData: HeroData = {
  title: "Where Vision Meets Intelligence",
  subtitle: "",
  description: "We don't just create AI — we forge alliances that transform strong products into market leaders. Through carefully chosen Joint Ventures, we bring the intelligence, you bring the spark, and together we ignite growth that lasts.",
  primaryCTA: "👉 Let's Build Together",
};

export const storyData: StoryData = {
  title: "Our Story",
  paragraphs: [
    "The world is overflowing with AI companies chasing the next buzzword. That's not us.",
    "We believe the future belongs to those who already have something real — a product, a service, a solution that's working — and just need the right catalyst to scale. That catalyst is our intelligence.",
    "We exist to merge proven ideas with breakthrough AI, not as vendors, but as true partners in creation."
  ]
};

export const whatWeDoData: WhatWeDoData = {
  title: "What We Do Today",
  subtitle: "Right now, our energy is dedicated to Joint Ventures with companies ready for their next leap forward.",
  cards: [
    {
      id: "identify",
      title: "Identify",
      description: "Products with untapped growth potential.",
      gradient: "from-blue-900/30 to-purple-900/30",
      borderColor: "border-blue-500/20"
    },
    {
      id: "embed",
      title: "Embed",
      description: "Our AI to enhance performance, reach, and efficiency.",
      gradient: "from-purple-900/30 to-green-900/30",
      borderColor: "border-purple-500/20"
    },
    {
      id: "share",
      title: "Share",
      description: "The journey — and the rewards — as equal partners.",
      gradient: "from-green-900/30 to-blue-900/30",
      borderColor: "border-green-500/20"
    }
  ],
  bottomText: {
    light: "This isn't a service.",
    bold: "It's a partnership designed to multiply possibility."
  }
};

export const tomorrowData: TomorrowData = {
  title: "Tomorrow",
  subtitle: "Our horizon is much bigger.",
  description: "The ventures we form today are the foundation for a future where AI reshapes industries:",
  industries: [
    {
      id: "energy",
      title: "Energy",
      description: "that runs smarter.",
      color: "yellow-400",
      gradient: "from-yellow-900/30 to-orange-900/30",
      borderColor: "border-yellow-500/20"
    },
    {
      id: "healthcare",
      title: "Healthcare",
      description: "that predicts, not just treats.",
      color: "red-400",
      gradient: "from-red-900/30 to-pink-900/30",
      borderColor: "border-red-500/20"
    },
    {
      id: "finance",
      title: "Finance",
      description: "that protects and empowers.",
      color: "green-400",
      gradient: "from-green-900/30 to-emerald-900/30",
      borderColor: "border-green-500/20"
    },
    {
      id: "agriculture",
      title: "Agriculture",
      description: "that sustains communities.",
      color: "blue-400",
      gradient: "from-blue-900/30 to-cyan-900/30",
      borderColor: "border-blue-500/20"
    },
    {
      id: "entertainment",
      title: "Entertainment",
      description: "that connects us more deeply.",
      color: "purple-400",
      gradient: "from-purple-900/30 to-indigo-900/30",
      borderColor: "border-purple-500/20"
    }
  ],
  bottomText: "We're not waiting for the future. We're building it, step by step, partnership by partnership."
};

export const partnershipData: PartnershipData = {
  title: "Why Partner With Us",
  subtitle: "Because we believe in:",
  benefits: [
    {
      id: "shared-growth",
      title: "Shared Growth",
      description: "We only win when you win.",
      iconBg: "from-blue-500 to-purple-500"
    },
    {
      id: "collaboration",
      title: "True Collaboration",
      description: "Not client and vendor, but equals.",
      iconBg: "from-purple-500 to-green-500"
    },
    {
      id: "vision",
      title: "Vision with Action",
      description: "Big dreams, grounded in execution.",
      iconBg: "from-green-500 to-yellow-500"
    },
    {
      id: "impact",
      title: "Impact",
      description: "Making products unforgettable.",
      iconBg: "from-yellow-500 to-red-500"
    }
  ],
  bottomText: {
    normal: "Your product already matters.",
    bold: "With our AI, it becomes unforgettable."
  }
};

export const techTrustData: TechTrustData = {
  title: "Tech + Trust",
  subtitle: "Behind the vision is a platform built for scale, security, and precision:",
  features: [
    {
      id: "protection",
      title: "Enterprise Protection",
      description: "Enterprise-level protection for sensitive data.",
      iconColor: "bg-blue-500"
    },
    {
      id: "infrastructure",
      title: "Scalable Infrastructure",
      description: "Infrastructure designed to grow with you.",
      iconColor: "bg-purple-500"
    },
    {
      id: "solutions",
      title: "Custom Solutions",
      description: "Custom AI solutions, engineered for your unique challenges.",
      iconColor: "bg-green-500"
    }
  ],
  bottomText: {
    normal: "We don't experiment on you.",
    bold: "We engineer with you."
  }
};

export const ctaData: CTAData = {
  title: "The future doesn't belong to those who wait. It belongs to those who build.",
  subtitle: "Join us, not as clients, but as partners.",
  description: "Together, we'll turn intelligence into impact.",
  primaryCTA: "👉 Start a Conversation",
  secondaryCTA: "Explore Partnerships"
};
