"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { ButtonProps } from "@/types";

const AIButton = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ variant = "primary", size = "md", className, children, loading, ...props }, ref) => {
    const baseClasses = "inline-flex items-center justify-center rounded-full font-semibold transition-all duration-300 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring disabled:pointer-events-none disabled:opacity-50";
    
    const variants = {
      primary: "btn-primary text-white",
      secondary: "btn-secondary text-white",
      outline: "border-2 border-blue-500/30 bg-transparent text-blue-400 hover:bg-blue-500/10 hover:border-blue-500/50"
    };
    
    const sizes = {
      sm: "h-9 px-6 text-sm",
      md: "h-12 px-8 text-base",
      lg: "h-14 px-12 text-lg"
    };

    return (
      <motion.div
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <button
          ref={ref}
          className={cn(
            baseClasses,
            variants[variant],
            sizes[size],
            className
          )}
          disabled={loading || props.disabled}
          {...props}
        >
          {loading ? "Loading..." : children}
        </button>
      </motion.div>
    );
  }
);

AIButton.displayName = "AIButton";

export { AIButton };
