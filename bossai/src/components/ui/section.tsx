"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { cn } from "@/lib/utils";
import { SectionProps } from "@/types";

interface ExtendedSectionProps extends SectionProps {
  background?: "default" | "dark" | "pattern" | "grid";
  padding?: "sm" | "md" | "lg" | "xl";
  id?: string;
}

const Section = React.forwardRef<HTMLElement, ExtendedSectionProps>(
  ({ className, children, background = "default", padding = "lg", id, ...props }, ref) => {
    const backgrounds = {
      default: "",
      dark: "bg-black/20",
      pattern: "ai-pattern",
      grid: "tech-grid"
    };
    
    const paddings = {
      sm: "py-12",
      md: "py-16",
      lg: "py-24",
      xl: "py-32"
    };

    return (
      <motion.section
        ref={ref}
        id={id}
        className={cn(
          "relative",
          backgrounds[background],
          paddings[padding],
          className
        )}
        initial={{ opacity: 0 }}
        whileInView={{ opacity: 1 }}
        transition={{ duration: 0.6 }}
        viewport={{ once: true, margin: "-100px" }}
        {...props}
      >
        {children}
      </motion.section>
    );
  }
);

Section.displayName = "Section";

export { Section };
