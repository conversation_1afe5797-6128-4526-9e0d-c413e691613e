"use client";

import * as React from "react";
import { motion, HTMLMotionProps } from "framer-motion";
import { cn } from "@/lib/utils";

interface AICardProps extends Omit<HTMLMotionProps<"div">, "children"> {
  variant?: "default" | "glass" | "gradient" | "neural";
  hover?: boolean;
  glow?: boolean;
  children?: React.ReactNode;
}

function AICard({ 
  className, 
  variant = "default", 
  hover = true, 
  glow = false,
  children,
  ...props 
}: AICardProps) {
  const baseClasses = "relative overflow-hidden rounded-xl border backdrop-blur-sm";
  
  const variants = {
    default: "bg-card/80 border-border/50 text-card-foreground",
    glass: "bg-white/5 border-white/10 text-white backdrop-blur-md",
    gradient: "bg-gradient-to-br from-blue-500/10 via-purple-500/10 to-green-500/10 border-blue-500/20 text-white",
    neural: "bg-slate-900/80 border-blue-500/30 text-white"
  };

  const hoverClasses = hover ? "card-hover cursor-pointer" : "";
  const glowClasses = glow ? "shadow-lg shadow-blue-500/20" : "shadow-sm";

  return (
    <motion.div
      className={cn(
        baseClasses,
        variants[variant],
        hoverClasses,
        glowClasses,
        className
      )}
      whileHover={hover ? { scale: 1.02, y: -4 } : undefined}
      transition={{ duration: 0.2, ease: "easeOut" }}
      {...props}
    >
      {/* Neural network pattern overlay for neural variant */}
      {variant === "neural" && (
        <div className="absolute inset-0 neural-network opacity-20" />
      )}
      
      {/* Gradient overlay for gradient variant */}
      {variant === "gradient" && (
        <div className="absolute inset-0 bg-gradient-to-br from-blue-500/5 via-transparent to-purple-500/5" />
      )}
      
      {/* Content */}
      <div className="relative z-10">
        {children}
      </div>
    </motion.div>
  );
}

function AICardHeader({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      className={cn(
        "flex flex-col space-y-1.5 p-6 pb-4",
        className
      )}
      {...props}
    />
  );
}

function AICardTitle({ className, ...props }: React.ComponentProps<"h3">) {
  return (
    <h3
      className={cn(
        "text-2xl font-semibold leading-none tracking-tight",
        className
      )}
      {...props}
    />
  );
}

function AICardDescription({ className, ...props }: React.ComponentProps<"p">) {
  return (
    <p
      className={cn(
        "text-sm text-muted-foreground opacity-80",
        className
      )}
      {...props}
    />
  );
}

function AICardContent({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div 
      className={cn("p-6 pt-0", className)} 
      {...props} 
    />
  );
}

function AICardFooter({ className, ...props }: React.ComponentProps<"div">) {
  return (
    <div
      className={cn(
        "flex items-center p-6 pt-0",
        className
      )}
      {...props}
    />
  );
}

// Specialized dashboard metric card
function AIMetricCard({ 
  title, 
  value, 
  change, 
  icon, 
  trend = "neutral",
  className,
  ...props 
}: {
  title: string;
  value: string | number;
  change?: string;
  icon?: React.ReactNode;
  trend?: "up" | "down" | "neutral";
  className?: string;
} & Omit<HTMLMotionProps<"div">, "children">) {
  const trendColors = {
    up: "text-green-400",
    down: "text-red-400", 
    neutral: "text-gray-400"
  };

  return (
    <AICard variant="glass" glow className={cn("p-6", className)} {...props}>
      <div className="flex items-center justify-between">
        <div className="space-y-2">
          <p className="text-sm font-medium text-gray-300">{title}</p>
          <p className="text-3xl font-bold text-white glow-text">{value}</p>
          {change && (
            <p className={cn("text-sm font-medium", trendColors[trend])}>
              {change}
            </p>
          )}
        </div>
        {icon && (
          <div className="text-blue-400 opacity-80">
            {icon}
          </div>
        )}
      </div>
    </AICard>
  );
}

export {
  AICard,
  AICardHeader,
  AICardFooter,
  AICardTitle,
  AICardDescription,
  AICardContent,
  AIMetricCard,
};
