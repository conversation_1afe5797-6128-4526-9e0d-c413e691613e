"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { easing } from "@/lib/utils";

interface FloatingOrbsProps {
  count?: number;
  className?: string;
}

const FloatingOrbs: React.FC<FloatingOrbsProps> = ({ count = 3, className = "" }) => {
  const orbs = Array.from({ length: count }, (_, i) => i);

  const getOrbVariants = (delay: number) => ({
    animate: {
      y: [0, -20, 10, 0],
      rotate: [0, 120, 240, 360],
      transition: {
        duration: 6,
        ease: easing.easeInOut,
        repeat: Infinity,
        delay,
      }
    }
  });

  const orbConfigs = [
    {
      size: "w-24 h-24",
      gradient: "bg-gradient-to-br from-blue-500/30 to-purple-500/30",
      position: "top-[20%] left-[10%]"
    },
    {
      size: "w-16 h-16",
      gradient: "bg-gradient-to-br from-green-500/30 to-blue-500/30",
      position: "top-[60%] right-[15%]"
    },
    {
      size: "w-20 h-20",
      gradient: "bg-gradient-to-br from-purple-500/30 to-green-500/30",
      position: "bottom-[30%] left-[20%]"
    }
  ];

  return (
    <>
      {orbs.map((_, index) => {
        const config = orbConfigs[index % orbConfigs.length];
        return (
          <motion.div
            key={index}
            className={`absolute rounded-full blur-sm ${config.size} ${config.gradient} ${config.position} ${className}`}
            variants={getOrbVariants(index * 2)}
            animate="animate"
          />
        );
      })}
    </>
  );
};

export { FloatingOrbs };
