'use client'

import React from 'react'
import { cn } from '@/lib/utils'

// Mobile-first responsive container
interface ResponsiveContainerProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full'
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

export function ResponsiveContainer({ 
  children, 
  className, 
  maxWidth = 'full',
  padding = 'md',
  ...props 
}: ResponsiveContainerProps) {
  const maxWidthClasses = {
    sm: 'max-w-sm',
    md: 'max-w-md', 
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full'
  }

  const paddingClasses = {
    none: '',
    sm: 'px-2 py-1',
    md: 'px-4 py-2',
    lg: 'px-6 py-4'
  }

  return (
    <div 
      className={cn(
        'w-full mx-auto',
        maxWidthClasses[maxWidth],
        paddingClasses[padding],
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

// Mobile-optimized grid system
interface ResponsiveGridProps extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  cols?: {
    mobile?: 1 | 2
    tablet?: 1 | 2 | 3 | 4
    desktop?: 1 | 2 | 3 | 4 | 5 | 6
  }
  gap?: 'sm' | 'md' | 'lg'
}

export function ResponsiveGrid({ 
  children, 
  className,
  cols = { mobile: 1, tablet: 2, desktop: 3 },
  gap = 'md',
  ...props 
}: ResponsiveGridProps) {
  const gapClasses = {
    sm: 'gap-2',
    md: 'gap-4',
    lg: 'gap-6'
  }

  const mobileColClasses = {
    1: 'grid-cols-1',
    2: 'grid-cols-2'
  }

  const tabletColClasses = {
    1: 'sm:grid-cols-1',
    2: 'sm:grid-cols-2',
    3: 'sm:grid-cols-3',
    4: 'sm:grid-cols-4'
  }

  const desktopColClasses = {
    1: 'lg:grid-cols-1',
    2: 'lg:grid-cols-2',
    3: 'lg:grid-cols-3',
    4: 'lg:grid-cols-4',
    5: 'lg:grid-cols-5',
    6: 'lg:grid-cols-6'
  }

  return (
    <div 
      className={cn(
        'grid',
        gapClasses[gap],
        mobileColClasses[cols.mobile || 1],
        tabletColClasses[cols.tablet || 2],
        desktopColClasses[cols.desktop || 3],
        className
      )}
      {...props}
    >
      {children}
    </div>
  )
}

// Touch-friendly button component
interface TouchButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  children: React.ReactNode
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'sm' | 'md' | 'lg' | 'touch'
  fullWidth?: boolean
}

export function TouchButton({ 
  children, 
  className,
  variant = 'primary',
  size = 'md',
  fullWidth = false,
  ...props 
}: TouchButtonProps) {
  const variantClasses = {
    primary: 'bg-blue-600 hover:bg-blue-700 text-white',
    secondary: 'bg-slate-600 hover:bg-slate-700 text-white',
    outline: 'border-2 border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white',
    ghost: 'text-blue-600 hover:bg-blue-50'
  }

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
    touch: 'px-6 py-4 text-base min-h-[48px]' // 48px minimum touch target
  }

  return (
    <button 
      className={cn(
        'rounded-lg font-medium transition-colors duration-200',
        'focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2',
        'active:scale-95 transform transition-transform',
        variantClasses[variant],
        sizeClasses[size],
        fullWidth && 'w-full',
        className
      )}
      {...props}
    >
      {children}
    </button>
  )
}

// Mobile-optimized text sizing
interface ResponsiveTextProps extends React.HTMLAttributes<HTMLElement> {
  children: React.ReactNode
  as?: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'p' | 'span'
  size?: {
    mobile?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl'
    desktop?: 'xs' | 'sm' | 'base' | 'lg' | 'xl' | '2xl' | '3xl' | '4xl' | '5xl'
  }
  weight?: 'normal' | 'medium' | 'semibold' | 'bold'
}

export function ResponsiveText({ 
  children, 
  className,
  as: Component = 'p',
  size = { mobile: 'base', desktop: 'lg' },
  weight = 'normal',
  ...props 
}: ResponsiveTextProps) {
  const mobileSizeClasses = {
    xs: 'text-xs',
    sm: 'text-sm',
    base: 'text-base',
    lg: 'text-lg',
    xl: 'text-xl',
    '2xl': 'text-2xl',
    '3xl': 'text-3xl'
  }

  const desktopSizeClasses = {
    xs: 'lg:text-xs',
    sm: 'lg:text-sm',
    base: 'lg:text-base',
    lg: 'lg:text-lg',
    xl: 'lg:text-xl',
    '2xl': 'lg:text-2xl',
    '3xl': 'lg:text-3xl',
    '4xl': 'lg:text-4xl',
    '5xl': 'lg:text-5xl'
  }

  const weightClasses = {
    normal: 'font-normal',
    medium: 'font-medium',
    semibold: 'font-semibold',
    bold: 'font-bold'
  }

  return (
    <Component 
      className={cn(
        mobileSizeClasses[size.mobile || 'base'],
        desktopSizeClasses[size.desktop || 'lg'],
        weightClasses[weight],
        className
      )}
      {...props}
    >
      {children}
    </Component>
  )
}

// Mobile-optimized spacing utilities
export const mobileSpacing = {
  // Padding classes optimized for mobile
  p: {
    xs: 'p-1 sm:p-2',
    sm: 'p-2 sm:p-3',
    md: 'p-3 sm:p-4',
    lg: 'p-4 sm:p-6',
    xl: 'p-6 sm:p-8'
  },
  // Margin classes optimized for mobile
  m: {
    xs: 'm-1 sm:m-2',
    sm: 'm-2 sm:m-3',
    md: 'm-3 sm:m-4',
    lg: 'm-4 sm:m-6',
    xl: 'm-6 sm:m-8'
  },
  // Gap classes for flex/grid
  gap: {
    xs: 'gap-1 sm:gap-2',
    sm: 'gap-2 sm:gap-3',
    md: 'gap-3 sm:gap-4',
    lg: 'gap-4 sm:gap-6',
    xl: 'gap-6 sm:gap-8'
  }
}

// Breakpoint utilities
export const breakpoints = {
  mobile: '(max-width: 640px)',
  tablet: '(min-width: 641px) and (max-width: 1024px)',
  desktop: '(min-width: 1025px)'
}

// Hook for responsive behavior
export function useResponsive() {
  const [isMobile, setIsMobile] = React.useState(false)
  const [isTablet, setIsTablet] = React.useState(false)
  const [isDesktop, setIsDesktop] = React.useState(false)

  React.useEffect(() => {
    const mobileQuery = window.matchMedia(breakpoints.mobile)
    const tabletQuery = window.matchMedia(breakpoints.tablet)
    const desktopQuery = window.matchMedia(breakpoints.desktop)

    const updateBreakpoints = () => {
      setIsMobile(mobileQuery.matches)
      setIsTablet(tabletQuery.matches)
      setIsDesktop(desktopQuery.matches)
    }

    updateBreakpoints()

    mobileQuery.addEventListener('change', updateBreakpoints)
    tabletQuery.addEventListener('change', updateBreakpoints)
    desktopQuery.addEventListener('change', updateBreakpoints)

    return () => {
      mobileQuery.removeEventListener('change', updateBreakpoints)
      tabletQuery.removeEventListener('change', updateBreakpoints)
      desktopQuery.removeEventListener('change', updateBreakpoints)
    }
  }, [])

  return { isMobile, isTablet, isDesktop }
}
