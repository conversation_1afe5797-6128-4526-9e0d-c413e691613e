"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AIButton } from "@/components/ui/ai-button";
import { easing } from "@/lib/utils";

// Comprehensive KYC/AML validation schema
const kycSchema = z.object({
  // Personal Information
  firstName: z.string().min(2, "First name is required"),
  lastName: z.string().min(2, "Last name is required"),
  middleName: z.string().optional(),
  dateOfBirth: z.string().min(1, "Date of birth is required"),
  nationality: z.string().min(2, "Nationality is required"),
  countryOfResidence: z.string().min(2, "Country of residence is required"),
  
  // Contact Information
  phone: z.string().min(10, "Valid phone number is required"),
  email: z.string().email("Valid email is required"),
  address: z.string().min(10, "Complete address is required"),
  city: z.string().min(2, "City is required"),
  state: z.string().min(2, "State/Province is required"),
  zipCode: z.string().min(3, "ZIP/Postal code is required"),
  
  // Identity Verification
  idType: z.enum(["passport", "drivers_license", "national_id"]),
  idNumber: z.string().min(5, "ID number is required"),
  idExpiryDate: z.string().min(1, "ID expiry date is required"),
  
  // Financial Information
  employmentStatus: z.enum(["employed", "self_employed", "unemployed", "retired", "student"]),
  employer: z.string().optional(),
  jobTitle: z.string().optional(),
  annualIncome: z.enum(["under_50k", "50k_100k", "100k_250k", "250k_500k", "500k_1m", "over_1m"]),
  netWorth: z.enum(["under_100k", "100k_500k", "500k_1m", "1m_5m", "5m_10m", "over_10m"]),
  sourceOfFunds: z.enum(["salary", "business", "investments", "inheritance", "other"]),
  sourceOfFundsOther: z.string().optional(),
  
  // Investment Experience
  investmentExperience: z.enum(["none", "limited", "moderate", "extensive"]),
  investmentTypes: z.array(z.string()).min(1, "Select at least one investment type"),
  riskTolerance: z.enum(["conservative", "moderate", "aggressive", "very_aggressive"]),
  
  // Compliance and Legal
  isPoliticallyExposed: z.boolean(),
  politicalExposureDetails: z.string().optional(),
  isUSPerson: z.boolean(),
  taxResidency: z.string().min(2, "Tax residency is required"),
  tinNumber: z.string().optional(),
  
  // AML Questionnaire
  hasBeenConvicted: z.boolean(),
  convictionDetails: z.string().optional(),
  hasBankruptcy: z.boolean(),
  bankruptcyDetails: z.string().optional(),
  hasRegulatorySanctions: z.boolean(),
  sanctionsDetails: z.string().optional(),
  
  // Agreements
  agreeToTerms: z.boolean().refine(val => val === true, "You must agree to the terms"),
  agreeToPrivacy: z.boolean().refine(val => val === true, "You must agree to the privacy policy"),
  agreeToDataProcessing: z.boolean().refine(val => val === true, "You must agree to data processing"),
  agreeToElectronicCommunication: z.boolean(),
});

type KYCFormData = z.infer<typeof kycSchema>;

export function KYCForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 6;

  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<KYCFormData>({
    resolver: zodResolver(kycSchema),
    defaultValues: {
      agreeToElectronicCommunication: true,
      isPoliticallyExposed: false,
      isUSPerson: false,
      hasBeenConvicted: false,
      hasBankruptcy: false,
      hasRegulatorySanctions: false,
    },
  });

  const onSubmit = async (data: KYCFormData) => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/kyc/submit", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data),
      });

      if (!response.ok) {
        throw new Error("Failed to submit KYC form");
      }

      // Handle success
      alert("KYC form submitted successfully! We will review your information and update your status within 2-3 business days.");
      
    } catch (error) {
      console.error("KYC submission error:", error);
      alert("Failed to submit KYC form. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => setCurrentStep(prev => Math.min(prev + 1, totalSteps));
  const prevStep = () => setCurrentStep(prev => Math.max(prev - 1, 1));

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.6,
        ease: easing.easeOut,
      },
    },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="max-w-4xl mx-auto"
    >
      <Card>
        <CardHeader>
          <CardTitle className="text-2xl gradient-text">
            KYC/AML Verification Form
          </CardTitle>
          <div className="flex items-center space-x-2 mt-4">
            {Array.from({ length: totalSteps }, (_, i) => (
              <div
                key={i}
                className={`h-2 flex-1 rounded-full ${
                  i + 1 <= currentStep ? "bg-primary" : "bg-muted"
                }`}
              />
            ))}
          </div>
          <p className="text-sm text-muted-foreground">
            Step {currentStep} of {totalSteps}
          </p>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Step 1: Personal Information */}
            {currentStep === 1 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Personal Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">First Name *</label>
                    <input
                      {...register("firstName")}
                      className="w-full p-3 border border-input rounded-lg bg-background"
                      placeholder="John"
                    />
                    {errors.firstName && (
                      <p className="text-destructive text-sm mt-1">{errors.firstName.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Middle Name</label>
                    <input
                      {...register("middleName")}
                      className="w-full p-3 border border-input rounded-lg bg-background"
                      placeholder="Optional"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Last Name *</label>
                    <input
                      {...register("lastName")}
                      className="w-full p-3 border border-input rounded-lg bg-background"
                      placeholder="Doe"
                    />
                    {errors.lastName && (
                      <p className="text-destructive text-sm mt-1">{errors.lastName.message}</p>
                    )}
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Date of Birth *</label>
                    <input
                      {...register("dateOfBirth")}
                      type="date"
                      className="w-full p-3 border border-input rounded-lg bg-background"
                    />
                    {errors.dateOfBirth && (
                      <p className="text-destructive text-sm mt-1">{errors.dateOfBirth.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Nationality *</label>
                    <select
                      {...register("nationality")}
                      className="w-full p-3 border border-input rounded-lg bg-background"
                    >
                      <option value="">Select nationality</option>
                      <option value="US">United States</option>
                      <option value="CA">Canada</option>
                      <option value="GB">United Kingdom</option>
                      <option value="AU">Australia</option>
                      <option value="DE">Germany</option>
                      <option value="FR">France</option>
                      <option value="other">Other</option>
                    </select>
                    {errors.nationality && (
                      <p className="text-destructive text-sm mt-1">{errors.nationality.message}</p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Step 2: Contact Information */}
            {currentStep === 2 && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold">Contact Information</h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">Email Address *</label>
                    <input
                      {...register("email")}
                      type="email"
                      className="w-full p-3 border border-input rounded-lg bg-background"
                      placeholder="<EMAIL>"
                    />
                    {errors.email && (
                      <p className="text-destructive text-sm mt-1">{errors.email.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">Phone Number *</label>
                    <input
                      {...register("phone")}
                      type="tel"
                      className="w-full p-3 border border-input rounded-lg bg-background"
                      placeholder="+****************"
                    />
                    {errors.phone && (
                      <p className="text-destructive text-sm mt-1">{errors.phone.message}</p>
                    )}
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-2">Street Address *</label>
                  <input
                    {...register("address")}
                    className="w-full p-3 border border-input rounded-lg bg-background"
                    placeholder="123 Main Street, Apt 4B"
                  />
                  {errors.address && (
                    <p className="text-destructive text-sm mt-1">{errors.address.message}</p>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">City *</label>
                    <input
                      {...register("city")}
                      className="w-full p-3 border border-input rounded-lg bg-background"
                      placeholder="New York"
                    />
                    {errors.city && (
                      <p className="text-destructive text-sm mt-1">{errors.city.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">State/Province *</label>
                    <input
                      {...register("state")}
                      className="w-full p-3 border border-input rounded-lg bg-background"
                      placeholder="NY"
                    />
                    {errors.state && (
                      <p className="text-destructive text-sm mt-1">{errors.state.message}</p>
                    )}
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">ZIP/Postal Code *</label>
                    <input
                      {...register("zipCode")}
                      className="w-full p-3 border border-input rounded-lg bg-background"
                      placeholder="10001"
                    />
                    {errors.zipCode && (
                      <p className="text-destructive text-sm mt-1">{errors.zipCode.message}</p>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Navigation Buttons */}
            <div className="flex justify-between pt-6">
              <AIButton
                type="button"
                variant="outline"
                onClick={prevStep}
                disabled={currentStep === 1}
              >
                Previous
              </AIButton>

              {currentStep < totalSteps ? (
                <AIButton
                  type="button"
                  onClick={nextStep}
                >
                  Next
                </AIButton>
              ) : (
                <AIButton
                  type="submit"
                  loading={isSubmitting}
                >
                  Submit KYC Form
                </AIButton>
              )}
            </div>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
}
