"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { useTheme } from "next-themes";
import { Moon, Sun, User, LogOut } from "lucide-react";
import { useSession, signOut } from "next-auth/react";
import Link from "next/link";
import { Container } from "@/components/ui/container";
import { AIButton } from "@/components/ui/ai-button";
import { easing } from "@/lib/utils";

const Navigation: React.FC = () => {
  const { theme, setTheme } = useTheme();
  const { data: session, status } = useSession();
  const [mounted, setMounted] = React.useState(false);

  React.useEffect(() => {
    setMounted(true);
  }, []);

  const navVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easing.easeOut
      }
    }
  };

  if (!mounted) return null;

  return (
    <motion.nav
      initial="hidden"
      animate="visible"
      variants={navVariants}
      className="fixed top-0 left-0 right-0 z-50 bg-background/80 backdrop-blur-md border-b border-border"
    >
      <Container>
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <motion.div
            whileHover={{ scale: 1.05 }}
            className="text-xl font-bold gradient-text cursor-pointer"
          >
            AI Venture
          </motion.div>

          {/* Navigation Links */}
          <div className="hidden md:flex items-center space-x-8">
            <a href="#story" className="text-foreground/80 hover:text-foreground transition-colors">
              Our Story
            </a>
            <a href="#what-we-do" className="text-foreground/80 hover:text-foreground transition-colors">
              What We Do
            </a>
            <a href="#tomorrow" className="text-foreground/80 hover:text-foreground transition-colors">
              Tomorrow
            </a>
            <a href="#partnership" className="text-foreground/80 hover:text-foreground transition-colors">
              Partnership
            </a>
          </div>

          {/* Theme Toggle & Auth */}
          <div className="flex items-center space-x-4">
            <button
              onClick={() => setTheme(theme === "dark" ? "light" : "dark")}
              className="p-2 rounded-full hover:bg-accent transition-colors"
            >
              {theme === "dark" ? (
                <Sun className="w-5 h-5" />
              ) : (
                <Moon className="w-5 h-5" />
              )}
            </button>

            {status === "loading" ? (
              <div className="w-8 h-8 animate-pulse bg-muted rounded-full" />
            ) : session ? (
              <div className="flex items-center space-x-3">
                <Link href="/dashboard">
                  <AIButton variant="outline" size="sm">
                    <User className="w-4 h-4 mr-2" />
                    Dashboard
                  </AIButton>
                </Link>
                <button
                  onClick={() => signOut()}
                  className="p-2 rounded-full hover:bg-accent transition-colors text-muted-foreground hover:text-foreground"
                >
                  <LogOut className="w-4 h-4" />
                </button>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <Link href="/auth/signin">
                  <AIButton variant="outline" size="sm">
                    Sign In
                  </AIButton>
                </Link>
                <Link href="/boss-ai-investments">
                  <AIButton variant="primary" size="sm">
                    Get Started
                  </AIButton>
                </Link>
              </div>
            )}
          </div>
        </div>
      </Container>
    </motion.nav>
  );
};

export { Navigation };
