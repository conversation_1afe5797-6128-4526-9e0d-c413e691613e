"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Container } from "@/components/ui/container";
import { easing } from "@/lib/utils";

const Footer: React.FC = () => {
  const footerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easing.easeOut
      }
    }
  };

  return (
    <motion.footer
      initial="hidden"
      whileInView="visible"
      viewport={{ once: true }}
      variants={footerVariants}
      className="bg-background border-t border-border py-12"
    >
      <Container>
        <div className="grid md:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="md:col-span-2">
            <div className="text-2xl font-bold gradient-text mb-4">
              AI Venture Platform
            </div>
            <p className="text-muted-foreground max-w-md">
              Forging alliances that transform strong products into market leaders through 
              carefully chosen Joint Ventures.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2 text-muted-foreground">
              <li>
                <a href="#story" className="hover:text-foreground transition-colors">
                  Our Story
                </a>
              </li>
              <li>
                <a href="#what-we-do" className="hover:text-foreground transition-colors">
                  What We Do
                </a>
              </li>
              <li>
                <a href="#tomorrow" className="hover:text-foreground transition-colors">
                  Tomorrow
                </a>
              </li>
              <li>
                <a href="#partnership" className="hover:text-foreground transition-colors">
                  Partnership
                </a>
              </li>
            </ul>
          </div>

          {/* Contact */}
          <div>
            <h3 className="font-semibold mb-4">Connect</h3>
            <ul className="space-y-2 text-muted-foreground">
              <li>
                <a href="#" className="hover:text-foreground transition-colors">
                  Start a Conversation
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-foreground transition-colors">
                  Explore Partnerships
                </a>
              </li>
              <li>
                <a href="#" className="hover:text-foreground transition-colors">
                  Tech Documentation
                </a>
              </li>
            </ul>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="border-t border-border mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <p className="text-muted-foreground text-sm">
            © 2024 AI Venture Platform. All rights reserved.
          </p>
          <div className="flex space-x-6 mt-4 md:mt-0">
            <a href="#" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
              Privacy Policy
            </a>
            <a href="#" className="text-muted-foreground hover:text-foreground transition-colors text-sm">
              Terms of Service
            </a>
          </div>
        </div>
      </Container>
    </motion.footer>
  );
};

export { Footer };
