"use client";

import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { ChevronDownIcon, ChevronRightIcon } from "@radix-ui/react-icons";

interface SidebarProps {
  userData: any;
  stats: any;
}

interface SidebarSection {
  id: string;
  title: string;
  isActive?: boolean;
  status?: "active" | "pending" | "attention" | "normal";
  items?: {
    label: string;
    href: string;
    isButton?: boolean;
    buttonStyle?: string;
    status?: "active" | "pending" | "attention";
  }[];
  summary?: React.ReactNode;
}

export function InvestmentSidebar({ userData, stats }: SidebarProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(["dashboard", "investments"])
  );

  const toggleSection = (sectionId: string) => {
    const newExpanded = new Set(expandedSections);
    if (newExpanded.has(sectionId)) {
      newExpanded.delete(sectionId);
    } else {
      newExpanded.add(sectionId);
    }
    setExpandedSections(newExpanded);
  };

  const sections: SidebarSection[] = [
    {
      id: "dashboard",
      title: "Dashboard / Home",
      isActive: true,
      summary: (
        <div className="ml-4 mt-2 text-sm text-muted-foreground space-y-1">
          <div>Total Invested: ${stats.totalInvested?.toLocaleString() || '0'}</div>
          <div className="flex justify-between">
            <span>Active Projects:</span>
            <span className="font-medium text-green-600">{stats.activeInvestments || 0}</span>
          </div>
          <div className="flex justify-between">
            <span>Pending Approvals:</span>
            <span className={`font-medium ${
              (stats.pendingInvestments || 0) > 0 ? "text-yellow-600" : "text-gray-500"
            }`}>
              {stats.pendingInvestments || 0}
            </span>
          </div>
        </div>
      ),
    },
    {
      id: "investments",
      title: "Investments / Projects",
      status: stats.activeInvestments > 0 ? "active" : "normal",
      items: [
        {
          label: "Current Investments",
          href: "#current-investments",
          status: stats.activeInvestments > 0 ? "active" : undefined
        },
        { label: "Available Opportunities", href: "#available-opportunities" },
        {
          label: "Invest Now",
          href: "/boss-ai-investments",
          isButton: true,
          buttonStyle: "bg-green-50 text-green-700 hover:bg-green-100"
        },
      ],
    },
    {
      id: "boss-ai-form",
      title: "Boss AI Investment Form",
      status: userData.kycStatus === "APPROVED" ? "active" : "attention",
      items: [
        { label: "Submit New Application", href: "/boss-ai-investments" },
        { label: "View Submitted Forms", href: "#submitted-forms" },
      ],
      summary: (
        <div className="ml-4 mt-2 text-sm text-muted-foreground">
          <div className="flex justify-between">
            <span>Status:</span>
            <span className={`font-medium ${
              userData.kycStatus === "APPROVED" ? "text-green-600" : "text-yellow-600"
            }`}>
              {userData.kycStatus === "APPROVED" ? "Ready to Submit" : "KYC Required"}
            </span>
          </div>
        </div>
      ),
    },
    {
      id: "analytics",
      title: "Portfolio Analytics",
      items: [
        { label: "Performance by Industry", href: "#performance" },
        { label: "Growth Over Time", href: "#growth" },
        { label: "Projected Returns", href: "#projections" },
        { label: "Risk Exposure", href: "#risk" },
      ],
    },
    {
      id: "documents",
      title: "Documents / KYC",
      items: [
        { label: "Identification", href: "#identification" },
        { label: "Investment Agreements", href: "#agreements" },
        { label: "Tax Forms", href: "#tax-forms" },
        { 
          label: "Upload Documents", 
          href: "#upload", 
          isButton: true, 
          buttonStyle: "bg-blue-50 text-blue-700 hover:bg-blue-100" 
        },
      ],
    },
    {
      id: "notifications",
      title: "Notifications / Alerts",
      items: [
        { label: "Project Milestones", href: "#milestones" },
        { label: "Payments & Dividends", href: "#payments" },
        { label: "Project Updates", href: "#updates" },
      ],
    },
    {
      id: "settings",
      title: "Account Settings",
      items: [
        { label: "Personal Information", href: "#personal-info" },
        { label: "Security & 2FA", href: "#security" },
        { label: "Bank Account / Payment", href: "#payment-setup" },
        { label: "Subscription Preferences", href: "#preferences" },
      ],
    },
    {
      id: "support",
      title: "Support / Help",
      items: [
        { label: "FAQs", href: "#faqs" },
        { label: "Contact Investment Advisor", href: "#contact" },
        { label: "Platform Tutorials", href: "#tutorials" },
        { 
          label: "Get Support", 
          href: "#get-support", 
          isButton: true, 
          buttonStyle: "bg-purple-50 text-purple-700 hover:bg-purple-100" 
        },
      ],
    },
  ];

  const getStatusColor = (status?: string) => {
    switch (status) {
      case "active":
        return "border-l-4 border-green-500 bg-green-50/50";
      case "pending":
        return "border-l-4 border-yellow-500 bg-yellow-50/50";
      case "attention":
        return "border-l-4 border-red-500 bg-red-50/50";
      default:
        return "";
    }
  };

  const getStatusIndicator = (status?: string) => {
    switch (status) {
      case "active":
        return <div className="w-2 h-2 bg-green-500 rounded-full"></div>;
      case "pending":
        return <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>;
      case "attention":
        return <div className="w-2 h-2 bg-red-500 rounded-full"></div>;
      default:
        return null;
    }
  };

  return (
    <nav className="space-y-1">
      {sections.map((section) => (
        <div key={section.id} className="mb-2">
          {/* Section Header */}
          <button
            onClick={() => section.items && toggleSection(section.id)}
            className={`w-full text-left p-3 rounded-lg transition-colors font-medium flex items-center justify-between ${
              section.isActive
                ? "bg-primary/10 border-l-4 border-primary text-primary"
                : `hover:bg-accent ${getStatusColor(section.status)}`
            }`}
          >
            <div className="flex items-center space-x-2">
              {getStatusIndicator(section.status)}
              <span>{section.title}</span>
            </div>
            {section.items && (
              <motion.div
                animate={{ rotate: expandedSections.has(section.id) ? 90 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <ChevronRightIcon className="h-4 w-4" />
              </motion.div>
            )}
          </button>

          {/* Section Summary (always visible for some sections) */}
          {section.summary && (section.isActive || !section.items) && section.summary}

          {/* Section Items (collapsible) */}
          {section.items && (
            <AnimatePresence>
              {expandedSections.has(section.id) && (
                <motion.div
                  initial={{ height: 0, opacity: 0 }}
                  animate={{ height: "auto", opacity: 1 }}
                  exit={{ height: 0, opacity: 0 }}
                  transition={{ duration: 0.2 }}
                  className="overflow-hidden"
                >
                  <div className="ml-4 space-y-1 mt-2">
                    {section.items.map((item, index) => (
                      <div key={index}>
                        {item.isButton ? (
                          <button
                            onClick={() => window.location.href = item.href}
                            className={`w-full text-left p-2 text-sm rounded font-medium transition-colors ${
                              item.buttonStyle || "bg-gray-50 text-gray-700 hover:bg-gray-100"
                            }`}
                          >
                            {item.label}
                          </button>
                        ) : (
                          <a
                            href={item.href}
                            className={`block p-2 text-sm hover:bg-accent rounded transition-colors flex items-center space-x-2 ${
                              getStatusColor(item.status)
                            }`}
                          >
                            {getStatusIndicator(item.status)}
                            <span>{item.label}</span>
                          </a>
                        )}
                      </div>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          )}
        </div>
      ))}
    </nav>
  );
}
