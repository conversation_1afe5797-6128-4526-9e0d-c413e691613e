"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { AIButton } from "@/components/ui/ai-button";
import { easing } from "@/lib/utils";
import Link from "next/link";
import { DashboardStats } from "@/types";

interface DashboardContentProps {
  user: any; // User with investments and documents
  stats: DashboardStats;
  userRole: string;
}

export function DashboardContent({ user, stats, userRole }: DashboardContentProps) {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        ease: easing.easeOut,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easing.easeOut,
      },
    },
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getKYCStatusColor = (status: string) => {
    switch (status) {
      case "APPROVED":
        return "text-green-500";
      case "PENDING_REVIEW":
        return "text-yellow-500";
      case "REJECTED":
        return "text-red-500";
      default:
        return "text-gray-500";
    }
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Stats Overview */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Investments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalInvestments ?? 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Portfolio Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats?.totalValue ?? 0)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Returns</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-500">{formatCurrency(stats?.totalReturns ?? 0)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Pending Applications</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.pendingInvestments ?? 0}</div>
          </CardContent>
        </Card>
      </motion.div>

      {/* KYC Status & Quick Actions */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* KYC Status */}
        <Card>
          <CardHeader>
            <CardTitle>KYC Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Current Status</p>
                <p className={`text-lg font-semibold ${getKYCStatusColor(user.kycStatus)}`}>
                  {user.kycStatus.replace("_", " ")}
                </p>
              </div>
              {user.kycStatus === "NOT_STARTED" && (
                <AIButton size="sm">
                  Start KYC
                </AIButton>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Link href="/boss-ai-investments">
              <AIButton className="w-full" variant="primary">
                New Investment Application
              </AIButton>
            </Link>
            <AIButton className="w-full" variant="outline">
              Upload Documents
            </AIButton>
            <AIButton className="w-full" variant="outline">
              Contact Support
            </AIButton>
          </CardContent>
        </Card>
      </motion.div>

      {/* Recent Investments */}
      <motion.div variants={itemVariants}>
        <Card>
          <CardHeader>
            <CardTitle>Recent Investments</CardTitle>
          </CardHeader>
          <CardContent>
            {user.investments.length > 0 ? (
              <div className="space-y-4">
                {user.investments.slice(0, 5).map((investment: any) => (
                  <div key={investment.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">{investment.type}</p>
                      <p className="text-sm text-muted-foreground">
                        {new Date(investment.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">{formatCurrency(Number(investment.amount))}</p>
                      <p className={`text-sm ${
                        investment.status === "APPROVED" ? "text-green-500" :
                        investment.status === "PENDING" ? "text-yellow-500" :
                        investment.status === "REJECTED" ? "text-red-500" : "text-gray-500"
                      }`}>
                        {investment.status}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-8">
                <p className="text-muted-foreground mb-4">No investments yet</p>
                <Link href="/boss-ai-investments">
                  <AIButton>Submit Your First Investment</AIButton>
                </Link>
              </div>
            )}
          </CardContent>
        </Card>
      </motion.div>

      {/* Admin Actions (if admin) */}
      {(userRole === "ADMIN" || userRole === "SUPER_ADMIN") && (
        <motion.div variants={itemVariants}>
          <Card>
            <CardHeader>
              <CardTitle>Admin Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Link href="/admin">
                <AIButton className="w-full" variant="secondary">
                  Admin Dashboard
                </AIButton>
              </Link>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </motion.div>
  );
}
