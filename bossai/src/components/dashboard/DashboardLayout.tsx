"use client";

import { motion, AnimatePresence } from "framer-motion";
import { useSession } from "next-auth/react";
import { useDashboard, type ActiveView, type AdminActiveView, type InvestorActiveView } from "@/contexts/DashboardContext";
import { DashboardSidebar } from "./DashboardSidebar";
import { DashboardHeader } from "./DashboardHeader";
import { MobileSidebar, MobileHeader, MobileBottomNav } from "./MobileSidebar";
import { ResponsiveContainer, useResponsive } from "@/components/ui/mobile-responsive";
import { FloatingOrbs } from "@/components/ui/floating-orbs";
import { Section } from "@/components/ui/section";
import { Container } from "@/components/ui/container";

// Admin Components
import { AdminDashboardHome } from "./views/admin/AdminDashboardHome";
import { AdminProjectsManagement } from "./views/admin/AdminProjectsManagement";

// Investor Components
import { InvestorDashboardHome } from "./views/investor/InvestorDashboardHome";

// Shared/Legacy Components (to be updated)
import { DashboardHome } from "./views/DashboardHome";
import { InvestmentsProjects } from "./views/InvestmentsProjects";
import { BossAIForm } from "./views/BossAIForm";
import { AccountSettings } from "./views/AccountSettings";
import { SupportHelp } from "./views/SupportHelp";

// New Functional Components
import { AnalyticsView } from "./views/AnalyticsView";
import { DocumentsView } from "./views/DocumentsView";
import { NotificationsView } from "./views/NotificationsView";
import { InvestorPortfolioView } from "./views/InvestorPortfolioView";
import { InvestorDocumentsView } from "./views/InvestorDocumentsView";
import { InvestorAccountView } from "./views/InvestorAccountView";
import { InvestorSupportView } from "./views/InvestorSupportView";
import { LoadingSpinner } from "@/components/ui/loading-spinner";
import { ErrorBoundary } from "@/components/ui/error-boundary";

interface DashboardLayoutProps {
  children?: React.ReactNode;
}

export function DashboardLayout({ children }: DashboardLayoutProps) {
  const { data: session } = useSession();
  const { state, setSidebarOpen, setActiveView } = useDashboard();
  const { activeView, isLoading, error, sidebarOpen } = state;
  const { isMobile } = useResponsive();

  const userRole = session?.user?.role || "INVESTOR";
  const isAdmin = userRole === "ADMIN" || userRole === "SUPER_ADMIN";

  const getViewTitle = (view: ActiveView) => {
    const titles: Record<string, string> = {
      'dashboard': 'Dashboard',
      'investor-dashboard': 'Dashboard',
      'portfolio': 'Portfolio',
      'analytics': 'Analytics',
      'investments': 'Investments',
      'documents': 'Documents',
      'notifications': 'Notifications',
      'account': 'Account',
      'boss-ai-form': 'Boss AI',
      'admin-dashboard': 'Admin Dashboard',
      'admin-projects': 'Project Management',
      'admin-investors': 'Investor Management',
      'admin-form-submissions': 'Form Submissions',
      'admin-analytics': 'Analytics',
      'admin-documents': 'Documents',
      'admin-notifications': 'Notifications',
      'admin-settings': 'Settings',
      'admin-support': 'Support'
    }
    return titles[view] || 'Dashboard'
  }

  // Render the appropriate view component based on activeView and user role
  const renderMainContent = () => {
    const viewKey = activeView; // For AnimatePresence

    // Admin Views
    if (isAdmin) {
      switch (activeView as AdminActiveView) {
        case "admin-dashboard":
          return <AdminDashboardHome key={viewKey} />;
        case "admin-projects":
          return <AdminProjectsManagement key={viewKey} />;
        case "admin-investors":
          return <div key={viewKey} className="text-white">Admin Investor Management - Coming Soon</div>;
        case "admin-form-submissions":
          return <div key={viewKey} className="text-white">Admin Form Submissions - Coming Soon</div>;
        case "admin-analytics":
          return <AnalyticsView key={viewKey} />;
        case "admin-documents":
          return <DocumentsView key={viewKey} />;
        case "admin-notifications":
          return <NotificationsView key={viewKey} />;
        case "admin-settings":
          return <AccountSettings key={viewKey} />;
        case "admin-support":
          return <SupportHelp key={viewKey} />;
        default:
          return <AdminDashboardHome key={viewKey} />;
      }
    }

    // Investor Views
    switch (activeView as InvestorActiveView) {
      case "investor-dashboard":
        return <InvestorDashboardHome key={viewKey} />;
      case "investor-projects":
        return <InvestmentsProjects key={viewKey} setActiveView={setActiveView} />;
      case "investor-boss-ai-form":
        return <BossAIForm key={viewKey} setActiveView={setActiveView} />;
      case "investor-analytics":
        return <InvestorPortfolioView key={viewKey} />;
      case "investor-documents":
        return <InvestorDocumentsView key={viewKey} />;
      case "investor-notifications":
        return <NotificationsView key={viewKey} />;
      case "investor-settings":
        return <InvestorAccountView key={viewKey} />;
      case "investor-support":
        return <InvestorSupportView key={viewKey} />;
      default:
        return <InvestorDashboardHome key={viewKey} />;
    }
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center">
        <div className="bg-white rounded-lg shadow-xl p-8 max-w-md w-full mx-4">
          <div className="text-center">
            <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
              <svg className="w-8 h-8 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Dashboard Error</h3>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors"
            >
              Reload Dashboard
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen gradient-bg ai-pattern relative overflow-hidden">
      {/* Floating Orbs Background */}
      <FloatingOrbs count={3} />

      {/* Neural Network Pattern Overlay */}
      <div className="neural-network" />

      <div className="flex h-screen relative z-10">
        {/* Desktop Sidebar */}
        <div className="hidden lg:block">
          <DashboardSidebar />
        </div>

        {/* Mobile Sidebar */}
        <MobileSidebar
          activeView={activeView}
          onViewChange={setActiveView}
          userRole={isAdmin ? 'admin' : 'investor'}
          userName={session?.user?.name || 'User'}
          userEmail={session?.user?.email || '<EMAIL>'}
        />

        {/* Main Content Area */}
        <div className="flex-1 flex flex-col overflow-hidden min-w-0 lg:ml-64">
          {/* Desktop Header */}
          <div className="hidden lg:block">
            <DashboardHeader />
          </div>

          {/* Mobile Header */}
          <MobileHeader
            title={getViewTitle(activeView)}
            subtitle={isAdmin ? 'Admin Panel' : 'Investment Platform'}
          />

          {/* Main Content */}
          <main className="flex-1 overflow-y-auto bg-transparent backdrop-blur-sm pb-20 lg:pb-0">
            <ResponsiveContainer
              padding={'none'}
              className="h-full"
            >
              <ErrorBoundary>
                {isLoading ? (
                  <div className="flex items-center justify-center h-full">
                    <LoadingSpinner size="lg" />
                  </div>
                ) : (
                  <AnimatePresence mode="wait">
                    <motion.div
                      key={activeView}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -20 }}
                      transition={{
                        duration: 0.3,
                        ease: [0.4, 0.0, 0.2, 1]
                      }}
                      className="h-full"
                    >
                      {renderMainContent()}
                    </motion.div>
                  </AnimatePresence>
                )}
              </ErrorBoundary>
            </ResponsiveContainer>
          </main>
        </div>

        {/* Mobile Sidebar Overlay */}
        <AnimatePresence>
          {sidebarOpen && (
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              transition={{ duration: 0.2 }}
              className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
              onClick={() => setSidebarOpen(false)}
            />
          )}
        </AnimatePresence>
      </div>

      {/* Mobile Bottom Navigation */}
      <MobileBottomNav
        activeView={activeView}
        onViewChange={setActiveView}
        userRole={isAdmin ? 'admin' : 'investor'}
      />
    </div>
  );
}

// Animation variants for content transitions
export const contentVariants = {
  initial: {
    opacity: 0,
    y: 20,
    scale: 0.98
  },
  animate: {
    opacity: 1,
    y: 0,
    scale: 1
  },
  exit: {
    opacity: 0,
    y: -20,
    scale: 0.98
  }
};

// Page wrapper component for consistent AI-inspired styling
export function DashboardPageWrapper({
  children,
  title,
  subtitle,
  actions
}: {
  children: React.ReactNode;
  title: string;
  subtitle?: string;
  actions?: React.ReactNode;
}) {
  return (
    <Section background="default" padding="lg" className="relative">
      <Container size="full" className="px-0">
        <motion.div
          variants={contentVariants}
          initial="initial"
          animate="animate"
          exit="exit"
          className="space-y-8"
        >
          {/* Page Header with AI styling */}
          <div className="flex items-center justify-between">
            <div>
              <motion.h1
                className="text-hero glow-text text-white font-bold"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                {title}
              </motion.h1>
              {subtitle && (
                <motion.p
                  className="text-xl text-gray-300 mt-3 font-light"
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.2 }}
                >
                  {subtitle}
                </motion.p>
              )}
            </div>
            {actions && (
              <motion.div
                className="flex items-center space-x-4"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: 0.3 }}
              >
                {actions}
              </motion.div>
            )}
          </div>

          {/* Page Content with AI card styling */}
          <motion.div
            className="space-y-6"
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            {children}
          </motion.div>
        </motion.div>
      </Container>
    </Section>
  );
}
