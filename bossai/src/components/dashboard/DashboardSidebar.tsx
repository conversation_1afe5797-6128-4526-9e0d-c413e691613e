"use client";

import { motion } from "framer-motion";
import { useSession } from "next-auth/react";
import { useDashboard, type ActiveView, type AdminActiveView, type InvestorActiveView } from "@/contexts/DashboardContext";
import { Badge } from "@/components/ui/badge";
import {
  HomeIcon,
  BarChartIcon,
  FileTextIcon,
  PieChartIcon,
  ArchiveIcon,
  BellIcon,
  GearIcon,
  QuestionMarkCircledIcon,
  Cross1Icon,
  PersonIcon,
  ClipboardIcon,
  DashboardIcon,
  ComponentInstanceIcon
} from "@radix-ui/react-icons";

interface SidebarButton {
  id: ActiveView;
  label: string;
  icon: React.ComponentType<any>;
  badge?: number;
  description: string;
}

export function DashboardSidebar() {
  const { data: session } = useSession();
  const { state, setActiveView, setSidebarOpen } = useDashboard();
  const { activeView, sidebarOpen, portfolioStats, notifications } = state;

  const userRole = session?.user?.role || "INVESTOR";
  const isAdmin = userRole === "ADMIN" || userRole === "SUPER_ADMIN";

  // Calculate notification count with multiple safety checks
  const unreadNotifications = (notifications && Array.isArray(notifications)) 
    ? notifications.filter(n => n && typeof n === 'object' && !n.isRead).length 
    : 0;

  // Admin Sidebar Buttons
  const adminSidebarButtons: SidebarButton[] = [
    {
      id: "admin-dashboard" as AdminActiveView,
      label: "Dashboard / Overview",
      icon: DashboardIcon,
      description: "Platform metrics, user activity, and system health monitoring"
    },
    {
      id: "admin-projects" as AdminActiveView,
      label: "Projects / Investments",
      icon: ComponentInstanceIcon,
      description: "Manage investment opportunities, project approvals, and funding rounds"
    },
    {
      id: "admin-users" as AdminActiveView,
      label: "Users / Investors",
      icon: PersonIcon,
      description: "User management, investor profiles, and account verification"
    },
    {
      id: "admin-analytics" as AdminActiveView,
      label: "Analytics / Reports",
      icon: BarChartIcon,
      description: "Financial reports, performance analytics, and investment insights"
    },
    {
      id: "admin-documents" as AdminActiveView,
      label: "Documents / Compliance",
      icon: ArchiveIcon,
      description: "Verify and approve investor documents; track KYC submissions"
    },
    {
      id: "admin-notifications" as AdminActiveView,
      label: "Notifications / Alerts",
      icon: BellIcon,
      badge: unreadNotifications,
      description: "Review pending alerts: approvals, new submissions, expiring projects"
    },
    {
      id: "admin-settings" as AdminActiveView,
      label: "Account Settings",
      icon: GearIcon,
      description: "Platform configuration, security settings, and admin preferences"
    },
    {
      id: "admin-support" as AdminActiveView,
      label: "Support / Help",
      icon: QuestionMarkCircledIcon,
      description: "User support tickets, documentation, and system assistance"
    }
  ];

  // Investor Sidebar Buttons
  const investorSidebarButtons: SidebarButton[] = [
    {
      id: "investor-dashboard" as InvestorActiveView,
      label: "Dashboard / Home",
      icon: HomeIcon,
      description: "Portfolio overview, recent activity, and investment summary"
    },
    {
      id: "investor-projects" as InvestorActiveView,
      label: "Browse / Invest",
      icon: ComponentInstanceIcon,
      description: "Explore investment opportunities and manage your portfolio"
    },
    {
      id: "investor-boss-ai-form" as InvestorActiveView,
      label: "Boss AI Investment",
      icon: FileTextIcon,
      description: "Exclusive AI venture investment opportunity with high returns"
    },
    {
      id: "investor-analytics" as InvestorActiveView,
      label: "Portfolio / Analytics",
      icon: PieChartIcon,
      description: "Track performance, returns, and investment analytics"
    },
    {
      id: "investor-documents" as InvestorActiveView,
      label: "Documents / KYC",
      icon: ArchiveIcon,
      description: "Upload and manage legal documents; track verification status"
    },
    {
      id: "investor-notifications" as InvestorActiveView,
      label: "Notifications / Alerts",
      icon: BellIcon,
      badge: unreadNotifications,
      description: "Investment updates, approvals, and system alerts"
    },
    {
      id: "investor-settings" as InvestorActiveView,
      label: "Account Settings",
      icon: GearIcon,
      description: "Profile management, security settings, and preferences"
    },
    {
      id: "investor-support" as InvestorActiveView,
      label: "Support / Help",
      icon: QuestionMarkCircledIcon,
      description: "Get help, contact support, and access documentation"
    }
  ];

  const sidebarButtons = isAdmin ? adminSidebarButtons : investorSidebarButtons;

  const handleButtonClick = (viewId: ActiveView) => {
    setActiveView(viewId);
    // Close sidebar on mobile after selection
    if (window.innerWidth < 1024) {
      setSidebarOpen(false);
    }
  };

  return (
    <>
      {/* Desktop Sidebar - Always visible */}
      <div className="hidden lg:flex lg:flex-col lg:w-64 lg:fixed lg:inset-y-0 lg:left-0 lg:z-30 bg-slate-900/95 border-r border-slate-700/50 backdrop-blur-xl shadow-2xl">
        {/* Header */}
        <div className="flex items-center h-20 px-6 border-b border-slate-700/50 bg-slate-800/50">
          <div className="flex items-center space-x-3">
            <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
              <span className="text-white font-bold text-lg">BA</span>
            </div>
            <div>
              <h1 className="text-lg font-semibold text-white">Boss AI</h1>
              <p className="text-xs text-slate-400">
                {isAdmin ? "Admin Dashboard" : "Investment Platform"}
              </p>
            </div>
          </div>
        </div>

        {/* Portfolio Summary */}
        {portfolioStats && (
          <div className="p-6 border-b border-slate-700/50 bg-slate-800/30">
            <h3 className="text-sm font-medium text-slate-300 mb-4">Portfolio Overview</h3>
            <div className="space-y-3">
              <div className="flex justify-between items-center">
                <span className="text-xs text-slate-400">Total Invested</span>
                <span className="text-sm font-medium text-white">
                  ${(portfolioStats.totalInvested || 0).toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-slate-400">Current Value</span>
                <span className="text-sm font-medium text-white">
                  ${(portfolioStats.totalValue || 0).toLocaleString()}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-xs text-slate-400">Total Returns</span>
                <span className="text-sm font-medium text-emerald-400">
                  +${(portfolioStats.totalReturns || 0).toLocaleString()}
                </span>
              </div>
              <div className="pt-3 border-t border-slate-700/50">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-slate-400">Growth</span>
                  <span className="text-sm font-medium text-emerald-400">
                    +{(portfolioStats.portfolioGrowth || 0).toFixed(1)}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Navigation */}
        <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
          {sidebarButtons.map((button) => (
            <button
              key={button.id}
              onClick={() => handleButtonClick(button.id)}
              className={`w-full group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                activeView === button.id
                  ? 'bg-blue-600 text-white shadow-sm'
                  : 'text-slate-300 hover:bg-slate-700/50 hover:text-white'
              }`}
            >
              <button.icon 
                className={`mr-3 h-5 w-5 transition-colors ${
                  activeView === button.id ? 'text-white' : 'text-slate-400 group-hover:text-slate-300'
                }`} 
              />
              <div className="flex-1 text-left">
                <div className="font-medium">{button.label}</div>
                <div className={`text-xs mt-0.5 ${
                  activeView === button.id ? 'text-blue-100' : 'text-slate-500'
                }`}>
                  {button.description}
                </div>
              </div>
              {button.badge !== undefined && button.badge > 0 && (
                <Badge 
                  variant="secondary"
                  className={`ml-2 ${
                    activeView === button.id 
                      ? 'bg-blue-500 text-white' 
                      : 'bg-slate-600 text-slate-200'
                  }`}
                >
                  {button.badge}
                </Badge>
              )}
            </button>
          ))}
        </nav>

        {/* Footer */}
        <div className="p-4 border-t border-slate-700/50 bg-slate-800/30">
          <div className="text-center">
            <p className="text-xs text-slate-400">Boss AI Investment Platform</p>
            <p className="text-xs text-slate-500 mt-1">Secure • Professional • Scalable</p>
          </div>
        </div>
      </div>

      {/* Mobile Sidebar */}
      <motion.div
        initial={false}
        animate={{
          x: sidebarOpen ? 0 : -320,
        }}
        transition={{
          type: "spring",
          stiffness: 300,
          damping: 30
        }}
        className="lg:hidden fixed inset-y-0 left-0 z-50 w-80 bg-slate-900/95 border-r border-slate-700/50 backdrop-blur-xl shadow-2xl"
      >
        <div className="flex flex-col h-full">
          {/* Mobile Header */}
          <div className="flex items-center h-20 px-6 border-b border-slate-700/50 bg-slate-800/50">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center shadow-lg">
                <span className="text-white font-bold text-lg">BA</span>
              </div>
              <div>
                <h1 className="text-lg font-semibold text-white">Boss AI</h1>
                <p className="text-xs text-slate-400">
                  {isAdmin ? "Admin Dashboard" : "Investment Platform"}
                </p>
              </div>
            </div>
            <button
              onClick={() => setSidebarOpen(false)}
              className="ml-auto p-2 rounded-lg hover:bg-slate-700/50 text-slate-400 hover:text-white transition-colors"
            >
              <Cross1Icon className="h-5 w-5" />
            </button>
          </div>
          
          {/* Mobile Portfolio Summary */}
          {portfolioStats && (
            <div className="p-6 border-b border-slate-700/50 bg-slate-800/30">
              <h3 className="text-sm font-medium text-slate-300 mb-4">Portfolio Overview</h3>
              <div className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-xs text-slate-400">Total Invested</span>
                  <span className="text-sm font-medium text-white">
                    ${(portfolioStats.totalInvested || 0).toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-slate-400">Current Value</span>
                  <span className="text-sm font-medium text-white">
                    ${(portfolioStats.totalValue || 0).toLocaleString()}
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-xs text-slate-400">Total Returns</span>
                  <span className="text-sm font-medium text-emerald-400">
                    +${(portfolioStats.totalReturns || 0).toLocaleString()}
                  </span>
                </div>
                <div className="pt-3 border-t border-slate-700/50">
                  <div className="flex justify-between items-center">
                    <span className="text-xs text-slate-400">Growth</span>
                    <span className="text-sm font-medium text-emerald-400">
                      +{(portfolioStats.portfolioGrowth || 0).toFixed(1)}%
                    </span>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Mobile Navigation */}
          <nav className="flex-1 px-3 py-4 space-y-1 overflow-y-auto">
            {sidebarButtons.map((button) => (
              <button
                key={button.id}
                onClick={() => handleButtonClick(button.id)}
                className={`w-full group flex items-center px-3 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                  activeView === button.id
                    ? 'bg-blue-600 text-white shadow-sm'
                    : 'text-slate-300 hover:bg-slate-700/50 hover:text-white'
                }`}
              >
                <button.icon 
                  className={`mr-3 h-5 w-5 transition-colors ${
                    activeView === button.id ? 'text-white' : 'text-slate-400 group-hover:text-slate-300'
                  }`} 
                />
                <div className="flex-1 text-left">
                  <div className="font-medium">{button.label}</div>
                  <div className={`text-xs mt-0.5 ${
                    activeView === button.id ? 'text-blue-100' : 'text-slate-500'
                  }`}>
                    {button.description}
                  </div>
                </div>
                {button.badge !== undefined && button.badge > 0 && (
                  <Badge 
                    variant="secondary"
                    className={`ml-2 ${
                      activeView === button.id 
                        ? 'bg-blue-500 text-white' 
                        : 'bg-slate-600 text-slate-200'
                    }`}
                  >
                    {button.badge}
                  </Badge>
                )}
              </button>
            ))}
          </nav>

          {/* Mobile Footer */}
          <div className="p-4 border-t border-slate-700/50 bg-slate-800/30">
            <div className="text-center">
              <p className="text-xs text-slate-400">Boss AI Investment Platform</p>
              <p className="text-xs text-slate-500 mt-1">Secure • Professional • Scalable</p>
            </div>
          </div>
        </div>
      </motion.div>
    </>
  );
}
