"use client";

import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { AIButton } from "@/components/ui/ai-button";
import { Badge } from "@/components/ui/badge";

interface BossAIFormProps {
  user: any;
  userData: any;
}

export function BossAIForm({ user, userData }: BossAIFormProps) {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold gradient-text">Boss AI Investment Form</h1>
        <p className="text-muted-foreground mt-2">Submit new investment applications</p>
      </div>

      {/* KYC Status Check */}
      <Card>
        <CardHeader>
          <CardTitle>Application Status</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center space-x-3 mb-4">
            <div className={`w-3 h-3 rounded-full ${
              userData.kycStatus === "APPROVED" ? "bg-green-500" : "bg-yellow-500"
            }`}></div>
            <span className={`font-semibold ${
              userData.kycStatus === "APPROVED" ? "text-green-600" : "text-yellow-600"
            }`}>
              {userData.kycStatus === "APPROVED" ? "Ready to Submit" : "KYC Verification Required"}
            </span>
          </div>
          <p className="text-sm text-muted-foreground">
            {userData.kycStatus === "APPROVED" 
              ? "Your KYC verification is complete. You can now submit investment applications."
              : "Please complete your KYC verification in the Documents section before submitting applications."
            }
          </p>
        </CardContent>
      </Card>

      {/* Investment Application Form */}
      <Card>
        <CardHeader>
          <CardTitle>New Investment Application</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Investment Amount</label>
              <input 
                type="number" 
                placeholder="$50,000"
                className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                disabled={userData.kycStatus !== "APPROVED"}
              />
              <p className="text-xs text-muted-foreground mt-1">Minimum investment: $50,000</p>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Project Type</label>
              <select 
                className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                disabled={userData.kycStatus !== "APPROVED"}
              >
                <option value="">Select project type</option>
                <option value="ai-technology">AI/Technology</option>
                <option value="clean-energy">Clean Energy</option>
                <option value="healthcare">Healthcare Innovation</option>
                <option value="fintech">Financial Technology</option>
                <option value="biotech">Biotechnology</option>
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Investment Timeline</label>
              <select 
                className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                disabled={userData.kycStatus !== "APPROVED"}
              >
                <option value="">Select timeline</option>
                <option value="short-term">Short-term (1-2 years)</option>
                <option value="medium-term">Medium-term (3-5 years)</option>
                <option value="long-term">Long-term (5+ years)</option>
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Risk Tolerance</label>
              <select 
                className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
                disabled={userData.kycStatus !== "APPROVED"}
              >
                <option value="">Select risk level</option>
                <option value="conservative">Conservative</option>
                <option value="moderate">Moderate</option>
                <option value="aggressive">Aggressive</option>
              </select>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium mb-2">Investment Goals & Objectives</label>
            <textarea 
              placeholder="Describe your investment objectives, expected returns, and any specific requirements..."
              className="w-full p-3 border rounded-lg h-32 focus:ring-2 focus:ring-primary focus:border-transparent"
              disabled={userData.kycStatus !== "APPROVED"}
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-2">Additional Comments</label>
            <textarea 
              placeholder="Any additional information or special requests..."
              className="w-full p-3 border rounded-lg h-24 focus:ring-2 focus:ring-primary focus:border-transparent"
              disabled={userData.kycStatus !== "APPROVED"}
            />
          </div>
          
          <AIButton 
            className="w-full btn-primary"
            disabled={userData.kycStatus !== "APPROVED"}
          >
            {userData.kycStatus === "APPROVED" ? "Submit Investment Application" : "Complete KYC First"}
          </AIButton>
        </CardContent>
      </Card>

      {/* Previous Applications */}
      <Card>
        <CardHeader>
          <CardTitle>Previous Applications</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">AI Technology Investment</h4>
                <p className="text-sm text-muted-foreground">Submitted: March 15, 2024</p>
                <p className="text-sm text-muted-foreground">Amount: $75,000</p>
              </div>
              <Badge className="bg-green-100 text-green-800">Approved</Badge>
            </div>
            
            <div className="flex items-center justify-between p-4 border rounded-lg">
              <div>
                <h4 className="font-medium">Clean Energy Project</h4>
                <p className="text-sm text-muted-foreground">Submitted: February 28, 2024</p>
                <p className="text-sm text-muted-foreground">Amount: $100,000</p>
              </div>
              <Badge className="bg-yellow-100 text-yellow-800">Under Review</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
