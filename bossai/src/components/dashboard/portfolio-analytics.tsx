"use client";

import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { TriangleUpIcon as TrendingUpIcon, TriangleDownIcon, PieChartIcon, BarChartIcon } from "@radix-ui/react-icons";

interface PortfolioAnalyticsProps {
  userData: any;
  stats: any;
}

export function PortfolioAnalytics({ userData, stats }: PortfolioAnalyticsProps) {
  const performanceData = [
    { month: "Jan", value: 45000, gains: 2000 },
    { month: "Feb", value: 52000, gains: 7000 },
    { month: "Mar", value: 48000, gains: -4000 },
    { month: "Apr", value: 61000, gains: 13000 },
    { month: "May", value: 55000, gains: -6000 },
    { month: "Jun", value: 67000, gains: 12000 },
  ];

  const industryAllocation = [
    { name: "AI/Technology", value: 35, color: "from-blue-500 to-cyan-500" },
    { name: "Clean Energy", value: 25, color: "from-green-500 to-emerald-500" },
    { name: "Healthcare", value: 20, color: "from-purple-500 to-pink-500" },
    { name: "Financial", value: 15, color: "from-orange-500 to-red-500" },
    { name: "Other", value: 5, color: "from-gray-500 to-slate-500" },
  ];

  const riskExposure = [
    { level: "Low Risk", percentage: 30, amount: 20100, color: "bg-green-500" },
    { level: "Medium Risk", percentage: 50, amount: 33500, color: "bg-yellow-500" },
    { level: "High Risk", percentage: 20, amount: 13400, color: "bg-red-500" },
  ];

  const topPerformers = [
    { name: "NeuralTech AI", roi: 50, investment: 15000, current: 22500 },
    { name: "HealthTech Innovations", roi: 85, investment: 18000, current: 33300 },
    { name: "FinTech Revolution", roi: 32, investment: 20000, current: 26400 },
  ];

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold gradient-text mb-2">Portfolio Analytics</h1>
        <p className="text-muted-foreground text-lg">
          Comprehensive analysis of your investment performance
        </p>
      </motion.div>

      {/* Summary Metrics */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Invested vs Gains</CardTitle>
            <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Invested</span>
                <span className="font-medium">$67,000</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Current Value</span>
                <span className="font-medium text-green-600">$82,200</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm text-muted-foreground">Total Gains</span>
                <span className="font-bold text-green-600">+$15,200</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Highest Performing</CardTitle>
            <TrendingUpIcon className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <p className="font-semibold">HealthTech Innovations</p>
              <p className="text-2xl font-bold text-green-600">+85%</p>
              <p className="text-xs text-muted-foreground">$18,000 → $33,300</p>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Lowest Performing</CardTitle>
            <TriangleDownIcon className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="space-y-1">
              <p className="font-semibold">GreenEnergy Solutions</p>
              <p className="text-2xl font-bold text-gray-500">0%</p>
              <p className="text-xs text-muted-foreground">Pending approval</p>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Charts Grid */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Portfolio Performance Chart */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChartIcon className="h-5 w-5" />
              <span>Portfolio Performance Over Time</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-end justify-between space-x-2">
              {performanceData.map((data, index) => (
                <div key={data.month} className="flex flex-col items-center space-y-2 flex-1">
                  <motion.div
                    initial={{ height: 0 }}
                    animate={{ height: `${(data.value / 70000) * 100}%` }}
                    transition={{ delay: index * 0.1, duration: 0.6 }}
                    className="w-full bg-gradient-to-t from-blue-500 to-purple-500 rounded-t-md min-h-[20px] relative group"
                  >
                    <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity">
                      ${data.value.toLocaleString()}
                    </div>
                  </motion.div>
                  <span className="text-xs text-muted-foreground">{data.month}</span>
                  <span className={`text-xs font-medium ${
                    data.gains >= 0 ? "text-green-600" : "text-red-600"
                  }`}>
                    {data.gains >= 0 ? "+" : ""}${data.gains.toLocaleString()}
                  </span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Industry Allocation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <PieChartIcon className="h-5 w-5" />
              <span>Industry Allocation</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {industryAllocation.map((industry, index) => (
                <motion.div
                  key={industry.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="space-y-2"
                >
                  <div className="flex justify-between text-sm">
                    <span>{industry.name}</span>
                    <span className="font-medium">{industry.value}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <motion.div
                      initial={{ width: 0 }}
                      animate={{ width: `${industry.value}%` }}
                      transition={{ delay: index * 0.2, duration: 0.8 }}
                      className={`bg-gradient-to-r ${industry.color} h-2 rounded-full`}
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Risk Exposure & Top Performers */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Risk Exposure Heatmap */}
        <Card>
          <CardHeader>
            <CardTitle>Risk Exposure</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {riskExposure.map((risk, index) => (
                <motion.div
                  key={risk.level}
                  initial={{ opacity: 0, scale: 0.9 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-4 rounded-lg bg-muted/30"
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-4 h-4 rounded-full ${risk.color}`} />
                    <div>
                      <p className="font-medium">{risk.level}</p>
                      <p className="text-sm text-muted-foreground">{risk.percentage}% of portfolio</p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">${risk.amount.toLocaleString()}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* ROI by Project */}
        <Card>
          <CardHeader>
            <CardTitle>ROI by Project</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {topPerformers.map((project, index) => (
                <motion.div
                  key={project.name}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-3 rounded-lg bg-muted/30"
                >
                  <div>
                    <p className="font-medium">{project.name}</p>
                    <p className="text-sm text-muted-foreground">
                      ${project.investment.toLocaleString()} → ${project.current.toLocaleString()}
                    </p>
                  </div>
                  <div className="text-right">
                    <p className={`text-lg font-bold ${
                      project.roi > 0 ? "text-green-600" : "text-red-600"
                    }`}>
                      +{project.roi}%
                    </p>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
