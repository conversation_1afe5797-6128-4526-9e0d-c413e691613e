"use client";

import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AIButton } from "@/components/ui/ai-button";
import { Badge } from "@/components/ui/badge";
import { 
  QuestionMarkCircledIcon, 
  ChatBubbleIcon, 
  EnvelopeClosedIcon, 
  VideoIcon,
  ChevronRightIcon
} from "@radix-ui/react-icons";

interface SupportHelpProps {
  user: any;
}

export function SupportHelp({ user }: SupportHelpProps) {
  const faqs = [
    {
      question: "How do I make my first investment?",
      answer: "Complete your KYC verification, then navigate to the Boss AI Investment Form to submit your application."
    },
    {
      question: "What documents are required for KYC?",
      answer: "You need a government-issued ID, proof of address, bank account verification, and source of funds declaration."
    },
    {
      question: "What are the minimum investment amounts?",
      answer: "The minimum investment amount is $50,000 per project, with a maximum of $1,000,000 per transaction for verified accounts."
    },
    {
      question: "How long does the investment approval process take?",
      answer: "Investment applications are typically reviewed within 3-5 business days after submission."
    },
    {
      question: "Can I withdraw my investment early?",
      answer: "Early withdrawal terms depend on the specific investment project. Please review the investment agreement for details."
    }
  ];

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold gradient-text">Support & Help</h1>
        <p className="text-muted-foreground mt-2">Get help with the platform and contact our investment advisors</p>
      </div>

      {/* Contact Options */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <ChatBubbleIcon className="h-8 w-8 text-blue-500 mx-auto mb-3" />
            <h3 className="font-medium mb-2">Live Chat</h3>
            <p className="text-sm text-muted-foreground mb-3">Chat with our support team</p>
            <Badge className="bg-green-100 text-green-800">Online</Badge>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <EnvelopeClosedIcon className="h-8 w-8 text-purple-500 mx-auto mb-3" />
            <h3 className="font-medium mb-2">Email Support</h3>
            <p className="text-sm text-muted-foreground mb-3">Get help via email</p>
            <Badge className="bg-blue-100 text-blue-800">24/7</Badge>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <VideoIcon className="h-8 w-8 text-green-500 mx-auto mb-3" />
            <h3 className="font-medium mb-2">Video Call</h3>
            <p className="text-sm text-muted-foreground mb-3">Schedule a consultation</p>
            <Badge className="bg-yellow-100 text-yellow-800">By Appointment</Badge>
          </CardContent>
        </Card>

        <Card className="hover:shadow-md transition-shadow cursor-pointer">
          <CardContent className="p-6 text-center">
            <QuestionMarkCircledIcon className="h-8 w-8 text-orange-500 mx-auto mb-3" />
            <h3 className="font-medium mb-2">Knowledge Base</h3>
            <p className="text-sm text-muted-foreground mb-3">Browse help articles</p>
            <Badge className="bg-gray-100 text-gray-800">Self-Service</Badge>
          </CardContent>
        </Card>
      </div>

      {/* Contact Form */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Contact Support</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">Subject</label>
              <select className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                <option value="">Select a topic</option>
                <option value="investment">Investment Questions</option>
                <option value="kyc">KYC/Document Issues</option>
                <option value="technical">Technical Support</option>
                <option value="account">Account Management</option>
                <option value="other">Other</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Priority</label>
              <select className="w-full p-3 border rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent">
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium mb-2">Message</label>
              <textarea 
                placeholder="Describe your issue or question in detail..."
                className="w-full p-3 border rounded-lg h-32 focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            
            <AIButton className="w-full btn-primary">
              Send Message
            </AIButton>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Support Information</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div>
                <h4 className="font-medium">Business Hours</h4>
                <p className="text-sm text-muted-foreground">Monday - Friday: 9:00 AM - 6:00 PM EST</p>
                <p className="text-sm text-muted-foreground">Saturday: 10:00 AM - 4:00 PM EST</p>
                <p className="text-sm text-muted-foreground">Sunday: Closed</p>
              </div>
              
              <div>
                <h4 className="font-medium">Response Times</h4>
                <p className="text-sm text-muted-foreground">• Live Chat: Immediate</p>
                <p className="text-sm text-muted-foreground">• Email: Within 4 hours</p>
                <p className="text-sm text-muted-foreground">• Phone: Within 1 hour</p>
              </div>
              
              <div>
                <h4 className="font-medium">Emergency Contact</h4>
                <p className="text-sm text-muted-foreground">For urgent investment matters:</p>
                <p className="text-sm font-medium">+****************</p>
                <p className="text-sm text-muted-foreground">Available 24/7</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* FAQ Section */}
      <Card>
        <CardHeader>
          <CardTitle>Frequently Asked Questions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {faqs.map((faq, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
              >
                <div className="flex items-center justify-between cursor-pointer">
                  <h4 className="font-medium">{faq.question}</h4>
                  <ChevronRightIcon className="h-4 w-4 text-muted-foreground" />
                </div>
                <p className="text-sm text-muted-foreground mt-2">{faq.answer}</p>
              </motion.div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Links */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Help Links</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-3">
              <h4 className="font-medium">Getting Started</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm text-blue-600 hover:underline cursor-pointer">
                  <ChevronRightIcon className="h-3 w-3" />
                  <span>Platform Overview</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-blue-600 hover:underline cursor-pointer">
                  <ChevronRightIcon className="h-3 w-3" />
                  <span>Account Setup Guide</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-blue-600 hover:underline cursor-pointer">
                  <ChevronRightIcon className="h-3 w-3" />
                  <span>First Investment Tutorial</span>
                </div>
              </div>
            </div>
            
            <div className="space-y-3">
              <h4 className="font-medium">Investment Guides</h4>
              <div className="space-y-2">
                <div className="flex items-center space-x-2 text-sm text-blue-600 hover:underline cursor-pointer">
                  <ChevronRightIcon className="h-3 w-3" />
                  <span>Risk Assessment Guide</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-blue-600 hover:underline cursor-pointer">
                  <ChevronRightIcon className="h-3 w-3" />
                  <span>Portfolio Diversification</span>
                </div>
                <div className="flex items-center space-x-2 text-sm text-blue-600 hover:underline cursor-pointer">
                  <ChevronRightIcon className="h-3 w-3" />
                  <span>Understanding Returns</span>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
