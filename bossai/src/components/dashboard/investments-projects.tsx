"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { AIButton } from "@/components/ui/ai-button";
import { Badge } from "@/components/ui/badge";
import { useInvestmentOpportunities } from "@/hooks/usePortfolio";

interface InvestmentsProjectsProps {
  user: any;
  stats: any;
}

export function InvestmentsProjects({ user, stats }: InvestmentsProjectsProps) {
  const [activeTab, setActiveTab] = useState<"current" | "opportunities">("current");

  const currentInvestments = [
    {
      id: 1,
      name: "NeuralTech AI",
      logo: "🤖",
      status: "Active",
      investment: 15000,
      roi: 50,
      nextMilestone: "2024-10-15",
      industry: "AI/ML",
      riskLevel: "Medium",
    },
    {
      id: 2,
      name: "GreenEnergy Solutions",
      logo: "🌱",
      status: "Pending",
      investment: 25000,
      roi: 0,
      nextMilestone: "2024-09-30",
      industry: "Clean Energy",
      riskLevel: "Low",
    },
    {
      id: 3,
      name: "FinTech Revolution",
      logo: "💳",
      status: "Active",
      investment: 20000,
      roi: 32,
      nextMilestone: "2024-11-01",
      industry: "Financial",
      riskLevel: "High",
    },
    {
      id: 4,
      name: "HealthTech Innovations",
      logo: "🏥",
      status: "Completed",
      investment: 18000,
      roi: 85,
      nextMilestone: "Exited",
      industry: "Healthcare",
      riskLevel: "Medium",
    },
  ];

  // Use real data from API instead of hardcoded data
  const { projects: opportunities, loading, error } = useInvestmentOpportunities();

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "active":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "completed":
        return "bg-blue-100 text-blue-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk.toLowerCase()) {
      case "low":
        return "bg-green-100 text-green-800";
      case "medium":
        return "bg-yellow-100 text-yellow-800";
      case "high":
        return "bg-orange-100 text-orange-800";
      case "very high":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Header */}
      <motion.div variants={itemVariants}>
        <h1 className="text-3xl font-bold gradient-text mb-2">Investments & Projects</h1>
        <p className="text-muted-foreground text-lg">
          Manage your current investments and explore new opportunities
        </p>
      </motion.div>

      {/* Tab Navigation */}
      <motion.div variants={itemVariants} className="flex space-x-1 bg-muted p-1 rounded-lg w-fit">
        <button
          onClick={() => setActiveTab("current")}
          className={`px-6 py-2 rounded-md font-medium transition-all ${
            activeTab === "current"
              ? "bg-background text-foreground shadow-sm"
              : "text-muted-foreground hover:text-foreground"
          }`}
        >
          Current Investments ({currentInvestments.length})
        </button>
        <button
          onClick={() => setActiveTab("opportunities")}
          className={`px-6 py-2 rounded-md font-medium transition-all ${
            activeTab === "opportunities"
              ? "bg-background text-foreground shadow-sm"
              : "text-muted-foreground hover:text-foreground"
          }`}
        >
          Available Opportunities ({opportunities.length})
        </button>
      </motion.div>

      {/* Current Investments Tab */}
      {activeTab === "current" && (
        <motion.div
          key="current"
          initial={{ opacity: 0, x: -20 }}
          animate={{ opacity: 1, x: 0 }}
          className="space-y-4"
        >
          {currentInvestments.map((investment, index) => (
            <motion.div
              key={investment.id}
              variants={itemVariants}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="hover:shadow-lg transition-shadow">
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-500 rounded-lg flex items-center justify-center text-2xl">
                        {investment.logo}
                      </div>
                      <div>
                        <h3 className="text-xl font-semibold">{investment.name}</h3>
                        <p className="text-muted-foreground">{investment.industry}</p>
                        <div className="flex items-center space-x-2 mt-2">
                          <Badge className={getStatusColor(investment.status)}>
                            {investment.status}
                          </Badge>
                          <Badge className={getRiskColor(investment.riskLevel)}>
                            {investment.riskLevel} Risk
                          </Badge>
                        </div>
                      </div>
                    </div>

                    <div className="text-right space-y-2">
                      <div>
                        <p className="text-sm text-muted-foreground">Investment</p>
                        <p className="text-lg font-semibold">${investment.investment.toLocaleString()}</p>
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground">ROI</p>
                        <p className={`text-lg font-bold ${
                          investment.roi > 0 ? "text-green-600" : "text-gray-500"
                        }`}>
                          {investment.roi > 0 ? "+" : ""}{investment.roi}%
                        </p>
                      </div>
                    </div>

                    <div className="text-right space-y-2">
                      <div>
                        <p className="text-sm text-muted-foreground">Next Milestone</p>
                        <p className="font-medium">
                          {investment.nextMilestone === "Exited" 
                            ? "Exited" 
                            : new Date(investment.nextMilestone).toLocaleDateString()
                          }
                        </p>
                      </div>
                      <AIButton variant="outline" size="sm">
                        View Details
                      </AIButton>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      )}

      {/* Available Opportunities Tab */}
      {activeTab === "opportunities" && (
        <motion.div
          key="opportunities"
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          className="grid grid-cols-1 lg:grid-cols-2 gap-6"
        >
          {opportunities.map((opportunity, index) => (
            <motion.div
              key={opportunity.id}
              variants={itemVariants}
              transition={{ delay: index * 0.1 }}
            >
              <Card className="hover:shadow-lg transition-shadow h-full">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className="w-12 h-12 bg-gradient-to-r from-purple-500 to-pink-500 rounded-lg flex items-center justify-center text-xl">
                      {opportunity.logo}
                    </div>
                    <div>
                      <CardTitle className="text-lg">{opportunity.name}</CardTitle>
                      <p className="text-sm text-muted-foreground">{opportunity.industry}</p>
                    </div>
                  </div>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-sm text-muted-foreground">{opportunity.description}</p>
                  
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <p className="text-xs text-muted-foreground">Min Investment</p>
                      <p className="font-semibold">${opportunity.minInvestment.toLocaleString()}</p>
                    </div>
                    <div>
                      <p className="text-xs text-muted-foreground">Expected ROI</p>
                      <p className="font-semibold text-green-600">+{opportunity.expectedROI}%</p>
                    </div>
                  </div>

                  <div>
                    <Badge className={getRiskColor(opportunity.riskLevel)}>
                      {opportunity.riskLevel} Risk
                    </Badge>
                  </div>

                  <div>
                    <div className="flex justify-between text-sm mb-2">
                      <span>Funding Progress</span>
                      <span>{Math.round((opportunity.currentFunding / opportunity.fundingGoal) * 100)}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <motion.div
                        initial={{ width: 0 }}
                        animate={{ width: `${(opportunity.currentFunding / opportunity.fundingGoal) * 100}%` }}
                        transition={{ delay: index * 0.2, duration: 1 }}
                        className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                      />
                    </div>
                    <div className="flex justify-between text-xs text-muted-foreground mt-1">
                      <span>${opportunity.currentFunding.toLocaleString()}</span>
                      <span>${opportunity.fundingGoal.toLocaleString()}</span>
                    </div>
                  </div>

                  <AIButton className="w-full" size="lg">
                    Invest Now
                  </AIButton>
                </CardContent>
              </Card>
            </motion.div>
          ))}
        </motion.div>
      )}
    </motion.div>
  );
}
