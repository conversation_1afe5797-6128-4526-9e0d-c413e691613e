'use client'

import React, { useState } from 'react'
import { motion, AnimatePresence } from 'framer-motion'
import { cn } from '@/lib/utils'
import { 
  HomeIcon, 
  ChartBarIcon, 
  DocumentTextIcon, 
  BellIcon,
  UserIcon,
  Bars3Icon,
  XMarkIcon,
  CogIcon,
  ArrowRightOnRectangleIcon
} from '@heroicons/react/24/outline'
import { TouchButton } from '@/components/ui/mobile-responsive'

interface MobileSidebarProps {
  activeView: string
  onViewChange: (view: string) => void
  userRole: 'investor' | 'admin'
  userName?: string
  userEmail?: string
}

export function MobileSidebar({ 
  activeView, 
  onViewChange, 
  userRole, 
  userName = 'User',
  userEmail = '<EMAIL>'
}: MobileSidebarProps) {
  const [isOpen, setIsOpen] = useState(false)

  const investorMenuItems = [
    { id: 'dashboard', label: 'Dashboard', icon: HomeIcon },
    { id: 'portfolio', label: 'Portfolio', icon: ChartBarIcon },
    { id: 'investments', label: 'Investments', icon: DocumentTextIcon },
    { id: 'boss-ai-form', label: 'Boss AI', icon: DocumentTextIcon },
    { id: 'documents', label: 'Documents', icon: DocumentTextIcon },
    { id: 'notifications', label: 'Notifications', icon: BellIcon },
    { id: 'account', label: 'Account', icon: UserIcon },
  ]

  const adminMenuItems = [
    { id: 'admin-dashboard', label: 'Dashboard', icon: HomeIcon },
    { id: 'admin-users', label: 'Users', icon: UserIcon },
    { id: 'admin-projects', label: 'Projects', icon: ChartBarIcon },
    { id: 'admin-analytics', label: 'Analytics', icon: ChartBarIcon },
    { id: 'admin-settings', label: 'Settings', icon: CogIcon },
  ]

  const menuItems = userRole === 'admin' ? adminMenuItems : investorMenuItems

  const toggleSidebar = () => setIsOpen(!isOpen)
  const closeSidebar = () => setIsOpen(false)

  const handleMenuItemClick = (viewId: string) => {
    onViewChange(viewId)
    closeSidebar()
  }

  return (
    <>
      {/* Mobile Menu Button - Fixed position */}
      <div className="lg:hidden fixed top-4 left-4 z-50">
        <TouchButton
          onClick={toggleSidebar}
          variant="primary"
          size="touch"
          className="bg-slate-800/90 backdrop-blur-sm border border-slate-600 shadow-lg"
        >
          {isOpen ? (
            <XMarkIcon className="h-6 w-6" />
          ) : (
            <Bars3Icon className="h-6 w-6" />
          )}
        </TouchButton>
      </div>

      {/* Backdrop */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            transition={{ duration: 0.2 }}
            className="lg:hidden fixed inset-0 bg-black/50 backdrop-blur-sm z-40"
            onClick={closeSidebar}
          />
        )}
      </AnimatePresence>

      {/* Mobile Sidebar */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ x: '-100%' }}
            animate={{ x: 0 }}
            exit={{ x: '-100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="lg:hidden fixed left-0 top-0 h-full w-80 max-w-[85vw] bg-slate-900/95 backdrop-blur-md border-r border-slate-700 z-50 overflow-y-auto"
          >
            {/* Header */}
            <div className="p-6 border-b border-slate-700">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold text-white">Boss AI</h2>
                <TouchButton
                  onClick={closeSidebar}
                  variant="ghost"
                  size="sm"
                  className="text-slate-400 hover:text-white"
                >
                  <XMarkIcon className="h-5 w-5" />
                </TouchButton>
              </div>
              
              {/* User Info */}
              <div className="flex items-center space-x-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
                  <UserIcon className="h-5 w-5 text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-white truncate">{userName}</p>
                  <p className="text-xs text-slate-400 truncate">{userEmail}</p>
                  <span className="inline-block px-2 py-0.5 text-xs bg-blue-600 text-white rounded-full mt-1">
                    {userRole === 'admin' ? 'Admin' : 'Investor'}
                  </span>
                </div>
              </div>
            </div>

            {/* Navigation Menu */}
            <nav className="p-4 space-y-2">
              {menuItems.map((item) => {
                const Icon = item.icon
                const isActive = activeView === item.id
                
                return (
                  <TouchButton
                    key={item.id}
                    onClick={() => handleMenuItemClick(item.id)}
                    variant={isActive ? 'primary' : 'ghost'}
                    size="touch"
                    fullWidth
                    className={cn(
                      'justify-start space-x-3 text-left',
                      isActive 
                        ? 'bg-blue-600 text-white shadow-lg' 
                        : 'text-slate-300 hover:text-white hover:bg-slate-800'
                    )}
                  >
                    <Icon className="h-5 w-5 flex-shrink-0" />
                    <span className="font-medium">{item.label}</span>
                  </TouchButton>
                )
              })}
            </nav>

            {/* Footer Actions */}
            <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-slate-700 bg-slate-900/95">
              <div className="space-y-2">
                <TouchButton
                  variant="ghost"
                  size="touch"
                  fullWidth
                  className="justify-start space-x-3 text-slate-300 hover:text-white hover:bg-slate-800"
                >
                  <CogIcon className="h-5 w-5" />
                  <span>Settings</span>
                </TouchButton>
                
                <TouchButton
                  variant="ghost"
                  size="touch"
                  fullWidth
                  className="justify-start space-x-3 text-red-400 hover:text-red-300 hover:bg-red-900/20"
                >
                  <ArrowRightOnRectangleIcon className="h-5 w-5" />
                  <span>Sign Out</span>
                </TouchButton>
              </div>
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Desktop Sidebar Spacer */}
      <div className="hidden lg:block w-64 flex-shrink-0" />
    </>
  )
}

// Mobile-optimized header component
interface MobileHeaderProps {
  title: string
  subtitle?: string
  actions?: React.ReactNode
}

export function MobileHeader({ title, subtitle, actions }: MobileHeaderProps) {
  return (
    <div className="lg:hidden bg-slate-900/95 backdrop-blur-md border-b border-slate-700 px-4 py-3 mb-4">
      <div className="flex items-center justify-between">
        <div className="flex-1 min-w-0 ml-16"> {/* Account for menu button */}
          <h1 className="text-lg font-semibold text-white truncate">{title}</h1>
          {subtitle && (
            <p className="text-sm text-slate-400 truncate">{subtitle}</p>
          )}
        </div>
        {actions && (
          <div className="flex-shrink-0 ml-4">
            {actions}
          </div>
        )}
      </div>
    </div>
  )
}

// Mobile-optimized bottom navigation (alternative to sidebar)
interface MobileBottomNavProps {
  activeView: string
  onViewChange: (view: string) => void
  userRole: 'investor' | 'admin'
}

export function MobileBottomNav({ activeView, onViewChange, userRole }: MobileBottomNavProps) {
  const investorNavItems = [
    { id: 'dashboard', label: 'Home', icon: HomeIcon },
    { id: 'portfolio', label: 'Portfolio', icon: ChartBarIcon },
    { id: 'investments', label: 'Invest', icon: DocumentTextIcon },
    { id: 'notifications', label: 'Alerts', icon: BellIcon },
    { id: 'account', label: 'Account', icon: UserIcon },
  ]

  const adminNavItems = [
    { id: 'admin-dashboard', label: 'Dashboard', icon: HomeIcon },
    { id: 'admin-users', label: 'Users', icon: UserIcon },
    { id: 'admin-projects', label: 'Projects', icon: ChartBarIcon },
    { id: 'admin-analytics', label: 'Analytics', icon: ChartBarIcon },
    { id: 'admin-settings', label: 'Settings', icon: CogIcon },
  ]

  const navItems = userRole === 'admin' ? adminNavItems : investorNavItems

  return (
    <div className="lg:hidden fixed bottom-0 left-0 right-0 bg-slate-900/95 backdrop-blur-md border-t border-slate-700 z-30">
      <div className="flex items-center justify-around px-2 py-2">
        {navItems.map((item) => {
          const Icon = item.icon
          const isActive = activeView === item.id
          
          return (
            <TouchButton
              key={item.id}
              onClick={() => onViewChange(item.id)}
              variant="ghost"
              size="sm"
              className={cn(
                'flex flex-col items-center space-y-1 min-w-0 flex-1 py-2',
                isActive 
                  ? 'text-blue-400' 
                  : 'text-slate-400 hover:text-white'
              )}
            >
              <Icon className="h-5 w-5 flex-shrink-0" />
              <span className="text-xs font-medium truncate">{item.label}</span>
            </TouchButton>
          )
        })}
      </div>
    </div>
  )
}
