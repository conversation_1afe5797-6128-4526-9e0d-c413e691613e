"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON><PERSON>ard, AICardContent, AICardHeader, AICardTitle, AIMetricCard } from "@/components/ui/ai-card";
import { AIButton } from "@/components/ui/ai-button";
import { Badge } from "@/components/ui/badge";
import { usePortfolio, useFeaturedProjects, useNotifications } from "@/hooks/usePortfolio";
import { DashboardSkeleton, ErrorState, StatCardSkeleton, ProjectCardSkeleton } from "@/components/ui/loading-skeleton";
import { ResponsiveGrid, ResponsiveContainer, ResponsiveText, TouchButton, useResponsive } from "@/components/ui/mobile-responsive";
import {
  CircleIcon as DollarSignIcon,
  TriangleUpIcon as TrendingUpIcon,
  ClockIcon,
  CheckCircledIcon as CheckCircleIcon,
  PlusIcon,
  BarChartIcon,
  FileTextIcon,
  ExclamationTriangleIcon as AlertCircleIcon
} from "@radix-ui/react-icons";

interface PersonalPortfolio {
  totalInvested: number;
  currentValue: number;
  totalReturns: number;
  activeInvestments: number;
  pendingInvestments: number;
  completedInvestments: number;
  portfolioGrowth: number;
}

interface RecentInvestment {
  id: string;
  projectTitle: string;
  amount: number;
  status: "PENDING" | "APPROVED" | "ACTIVE" | "COMPLETED";
  submittedAt: string;
  expectedReturn: number;
}

interface AvailableProject {
  id: string;
  title: string;
  sector: string;
  fundingGoal: number;
  currentFunding: number;
  roiEstimate: number;
  riskLevel: string;
  timeRemaining: string;
}

export function InvestorDashboardHome() {
  const { stats: portfolio, loading: portfolioLoading, error: portfolioError, refetch: refetchPortfolio } = usePortfolio();
  const { projects: featuredProjects, loading: projectsLoading, error: projectsError } = useFeaturedProjects();
  const { notifications, unreadCount, loading: notificationsLoading } = useNotifications();
  const { isMobile, isTablet } = useResponsive();

  // Show loading state while any critical data is loading
  if (portfolioLoading) {
    return <DashboardSkeleton />;
  }

  // Show error state if portfolio data failed to load
  if (portfolioError) {
    return <ErrorState message={portfolioError} onRetry={refetchPortfolio} />;
  }



  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusBadge = (status: string) => {
    const statusConfig: Record<string, { color: string; text: string }> = {
      PENDING: { color: "bg-yellow-500", text: "Pending" },
      UNDER_REVIEW: { color: "bg-blue-500", text: "Under Review" },
      APPROVED: { color: "bg-blue-500", text: "Approved" },
      ACTIVE: { color: "bg-green-500", text: "Active" },
      COMPLETED: { color: "bg-purple-500", text: "Completed" },
      REJECTED: { color: "bg-red-500", text: "Rejected" },
      CANCELLED: { color: "bg-gray-500", text: "Cancelled" }
    };

    const config = statusConfig[status] || { color: "bg-gray-500", text: status };
    return (
      <Badge className={`${config.color} text-white`}>
        {config.text}
      </Badge>
    );
  };

  const getFundingProgress = (current: number, goal: number) => {
    return Math.min((current / goal) * 100, 100);
  };



  return (
    <div className="px-4 lg:pl-0 lg:pr-6 py-6 space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold text-white glow-text">Welcome Back!</h1>
          <p className="text-xl text-blue-200 mt-2">Your personal investment dashboard</p>
        </div>
        <AIButton variant="primary" className="gap-2">
          <PlusIcon className="h-5 w-5" />
          New Investment
        </AIButton>
      </div>

      {/* Portfolio Overview with AI Metric Cards */}
      <ResponsiveGrid
        cols={{ mobile: 1, tablet: 2, desktop: 4 }}
        gap={isMobile ? 'sm' : 'md'}
      >
        <AIMetricCard
          title="Total Invested"
          value={formatCurrency(portfolio?.totalInvested || 0)}
          change={`${(portfolio?.monthlyGrowth ?? 0) >= 0 ? '+' : ''}${(portfolio?.monthlyGrowth ?? 0).toFixed(1)}% this month`}
          trend={(portfolio?.monthlyGrowth ?? 0) >= 0 ? 'up' : 'down'}
          icon={<DollarSignIcon className="h-8 w-8" />}
        />

        <AIMetricCard
          title="Current Value"
          value={formatCurrency(portfolio?.totalValue || 0)}
          change={`+${(portfolio?.averageReturn ?? 0).toFixed(1)}% total`}
          trend={(portfolio?.averageReturn ?? 0) >= 0 ? 'up' : 'down'}
          icon={<TrendingUpIcon className="h-8 w-8" />}
        />

        <AIMetricCard
          title="Total Return"
          value={formatCurrency(portfolio?.totalReturns || 0)}
          change={`${(portfolio?.portfolioGrowth ?? 0).toFixed(1)}% avg ROI`}
          trend={(portfolio?.portfolioGrowth ?? 0) >= 0 ? 'up' : 'down'}
          icon={<TrendingUpIcon className="h-8 w-8" />}
        />

        <AIMetricCard
          title="Active Projects"
          value={`${portfolio?.activeInvestments ?? 0}`}
          change={`${portfolio?.pendingInvestments ?? 0} pending`}
          trend="neutral"
          icon={<BarChartIcon className="h-8 w-8" />}
        />
      </ResponsiveGrid>

      {/* Recent Investments */}
      <AICard variant="glass">
        <AICardHeader>
          <AICardTitle>Recent Investments</AICardTitle>
        </AICardHeader>
        <AICardContent>
          <div className="space-y-4">
            {portfolio?.recentInvestments?.length > 0 ? (
              portfolio.recentInvestments.map((investment) => (
                <div key={investment.id} className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                  <div className="flex items-center space-x-4">
                    <div className="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                      <DollarSignIcon className="h-6 w-6 text-white" />
                    </div>
                    <div>
                      <h4 className="text-white font-semibold">
                        {investment.project?.title || `${investment.type} Investment`}
                      </h4>
                      <p className="text-slate-400 text-sm">
                        Invested {formatCurrency(investment.amount)} • Current: {formatCurrency(investment.currentValue)}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    {getStatusBadge(investment.status)}
                    <p className="text-slate-400 text-xs mt-1">
                      {new Date(investment.submittedAt).toLocaleDateString()}
                    </p>
                  </div>
                </div>
              ))
            ) : (
              <div className="text-center py-8 text-slate-400">
                <FileTextIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <p>No recent investments found</p>
                <p className="text-sm">Start investing to see your portfolio here</p>
              </div>
            )}
          </div>
        </AICardContent>
      </AICard>

      {/* Available Projects */}
      <AICard variant="gradient">
        <AICardHeader>
          <div className="flex items-center justify-between">
            <AICardTitle>Featured Investment Opportunities</AICardTitle>
            <AIButton variant="secondary">
              View All Projects
            </AIButton>
          </div>
        </AICardHeader>
        <AICardContent>
          {projectsLoading && (
            <ResponsiveGrid
              cols={{ mobile: 1, tablet: 2, desktop: 2 }}
              gap={isMobile ? 'sm' : 'md'}
            >
              {Array.from({ length: 4 }).map((_, i) => (
                <ProjectCardSkeleton key={i} />
              ))}
            </ResponsiveGrid>
          )}

          {projectsError && (
            <div className="text-center py-8 text-slate-400">
              <AlertCircleIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>Failed to load investment opportunities</p>
              <p className="text-sm">{projectsError}</p>
            </div>
          )}

          {!projectsLoading && !projectsError && featuredProjects?.length > 0 && (
            <ResponsiveGrid
              cols={{ mobile: 1, tablet: 2, desktop: 2 }}
              gap={isMobile ? 'sm' : 'md'}
            >
              {featuredProjects.map((project) => (
                <div key={project.id} className="p-4 bg-slate-700/30 rounded-lg">
                  <div className="flex items-start justify-between mb-3">
                    <div>
                      <h4 className="text-white font-semibold mb-1">{project.title}</h4>
                      <Badge variant="outline" className="text-slate-300 border-slate-600">
                        {project.sector}
                      </Badge>
                    </div>
                    <div className="text-right">
                      <p className="text-green-400 font-semibold">{project.roiEstimate}% ROI</p>
                      <p className="text-slate-400 text-xs">{project.riskLevel}</p>
                    </div>
                  </div>

                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span className="text-slate-400">Funding Progress</span>
                      <span className="text-white">
                        {formatCurrency(project.currentFunding)} / {formatCurrency(project.fundingGoal)}
                      </span>
                    </div>
                    <div className="w-full bg-slate-600 rounded-full h-2">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
                        style={{ width: project.fundingProgress + '%' }}
                      />
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center text-slate-400 text-sm">
                      <ClockIcon className="h-4 w-4 mr-1" />
                      {project.timeHorizon || 'Ongoing'}
                    </div>
                    <TouchButton variant="primary" size={isMobile ? 'touch' : 'sm'}>
                      Invest Now
                    </TouchButton>
                  </div>
                </div>
              ))}
            </ResponsiveGrid>
          )}

          {!projectsLoading && !projectsError && (!featuredProjects || featuredProjects.length === 0) && (
            <div className="text-center py-8 text-slate-400">
              <BarChartIcon className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <p>No featured projects available</p>
              <p className="text-sm">Check back later for new opportunities</p>
            </div>
          )}
        </AICardContent>
      </AICard>

      {/* Quick Actions */}
      <ResponsiveGrid
        cols={{ mobile: 1, tablet: 2, desktop: 3 }}
        gap={isMobile ? 'sm' : 'md'}
      >
        <AICard variant="glass" className="hover:scale-105 transition-transform cursor-pointer">
          <AICardContent className="p-6 text-center">
            <FileTextIcon className="h-12 w-12 text-blue-400 mx-auto mb-4" />
            <h3 className="text-white font-semibold mb-2 glow-text">Submit Investment</h3>
            <p className="text-blue-200 text-sm">Apply for new investment opportunities</p>
          </AICardContent>
        </AICard>

        <AICard variant="glass" className="hover:scale-105 transition-transform cursor-pointer">
          <AICardContent className="p-6 text-center">
            <BarChartIcon className="h-12 w-12 text-green-400 mx-auto mb-4" />
            <h3 className="text-white font-semibold mb-2 glow-text">View Analytics</h3>
            <p className="text-blue-200 text-sm">Track your portfolio performance</p>
          </AICardContent>
        </AICard>

        <AICard variant="glass" className="hover:scale-105 transition-transform cursor-pointer">
          <AICardContent className="p-6 text-center">
            <AlertCircleIcon className="h-12 w-12 text-yellow-400 mx-auto mb-4" />
            <h3 className="text-white font-semibold mb-2 glow-text">Upload Documents</h3>
            <p className="text-blue-200 text-sm">Complete your KYC verification</p>
          </AICardContent>
        </AICard>
      </ResponsiveGrid>
    </div>
  );
}
