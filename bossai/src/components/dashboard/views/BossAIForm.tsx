"use client";

import React, { useState } from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  DollarSignIcon,
  FileTextIcon,
  UploadIcon,
  CheckCircleIcon,
  AlertCircleIcon,
  InfoIcon,
  UserIcon,
  BuildingIcon,
  CreditCardIcon,
  ShieldCheckIcon
} from "lucide-react";
import { ActiveView } from "@/contexts/DashboardContext";

interface BossAIFormProps {
  setActiveView: (view: ActiveView) => void;
}

interface FormData {
  // Personal Information
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  dateOfBirth: string;

  // Investment Details
  investmentAmount: string;
  investmentType: string;
  riskTolerance: string;
  investmentGoals: string;
  timeHorizon: string;

  // Financial Information
  annualIncome: string;
  netWorth: string;
  liquidAssets: string;
  investmentExperience: string;

  // Legal and Compliance
  accreditedInvestor: boolean;
  agreeToTerms: boolean;
  agreeToPrivacy: boolean;

  // Documents
  documents: File[];
}

const initialFormData: FormData = {
  firstName: "",
  lastName: "",
  email: "",
  phone: "",
  dateOfBirth: "",
  investmentAmount: "",
  investmentType: "",
  riskTolerance: "",
  investmentGoals: "",
  timeHorizon: "",
  annualIncome: "",
  netWorth: "",
  liquidAssets: "",
  investmentExperience: "",
  accreditedInvestor: false,
  agreeToTerms: false,
  agreeToPrivacy: false,
  documents: [],
};

export function BossAIForm({ setActiveView }: BossAIFormProps) {
  const [formData, setFormData] = useState<FormData>(initialFormData);
  const [currentStep, setCurrentStep] = useState(1);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  const totalSteps = 4;

  const updateFormData = (field: keyof FormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: "" }));
    }
  };

  const validateStep = (step: number): boolean => {
    const newErrors: Record<string, string> = {};

    switch (step) {
      case 1: // Personal Information
        if (!formData.firstName.trim()) newErrors.firstName = "First name is required";
        if (!formData.lastName.trim()) newErrors.lastName = "Last name is required";
        if (!formData.email.trim()) newErrors.email = "Email is required";
        if (!formData.phone.trim()) newErrors.phone = "Phone number is required";
        if (!formData.dateOfBirth) newErrors.dateOfBirth = "Date of birth is required";
        break;

      case 2: // Investment Details
        if (!formData.investmentAmount) newErrors.investmentAmount = "Investment amount is required";
        if (!formData.investmentType) newErrors.investmentType = "Investment type is required";
        if (!formData.riskTolerance) newErrors.riskTolerance = "Risk tolerance is required";
        if (!formData.timeHorizon) newErrors.timeHorizon = "Time horizon is required";
        break;

      case 3: // Financial Information
        if (!formData.annualIncome) newErrors.annualIncome = "Annual income is required";
        if (!formData.netWorth) newErrors.netWorth = "Net worth is required";
        if (!formData.investmentExperience) newErrors.investmentExperience = "Investment experience is required";
        break;

      case 4: // Legal and Compliance
        if (!formData.agreeToTerms) newErrors.agreeToTerms = "You must agree to the terms and conditions";
        if (!formData.agreeToPrivacy) newErrors.agreeToPrivacy = "You must agree to the privacy policy";
        break;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleNext = () => {
    if (validateStep(currentStep)) {
      setCurrentStep(prev => Math.min(prev + 1, totalSteps));
    }
  };

  const handlePrevious = () => {
    setCurrentStep(prev => Math.max(prev - 1, 1));
  };

  const handleSubmit = async () => {
    if (!validateStep(currentStep)) return;

    setIsSubmitting(true);
    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Show success message and redirect
      alert("Investment application submitted successfully!");
      setActiveView("investor-projects");
    } catch (error) {
      alert("Error submitting application. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setFormData(prev => ({ ...prev, documents: [...prev.documents, ...files] }));
  };

  const removeDocument = (index: number) => {
    setFormData(prev => ({
      ...prev,
      documents: prev.documents.filter((_, i) => i !== index)
    }));
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div className="flex items-center mb-6">
              <UserIcon className="h-6 w-6 text-blue-400 mr-3" />
              <h3 className="text-xl font-semibold text-slate-100">Personal Information</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  First Name *
                </label>
                <input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => updateFormData("firstName", e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your first name"
                />
                {errors.firstName && (
                  <p className="text-red-400 text-sm mt-1">{errors.firstName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Last Name *
                </label>
                <input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => updateFormData("lastName", e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your last name"
                />
                {errors.lastName && (
                  <p className="text-red-400 text-sm mt-1">{errors.lastName}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Email Address *
                </label>
                <input
                  type="email"
                  value={formData.email}
                  onChange={(e) => updateFormData("email", e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your email address"
                />
                {errors.email && (
                  <p className="text-red-400 text-sm mt-1">{errors.email}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Phone Number *
                </label>
                <input
                  type="tel"
                  value={formData.phone}
                  onChange={(e) => updateFormData("phone", e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Enter your phone number"
                />
                {errors.phone && (
                  <p className="text-red-400 text-sm mt-1">{errors.phone}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Date of Birth *
                </label>
                <input
                  type="date"
                  value={formData.dateOfBirth}
                  onChange={(e) => updateFormData("dateOfBirth", e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                />
                {errors.dateOfBirth && (
                  <p className="text-red-400 text-sm mt-1">{errors.dateOfBirth}</p>
                )}
              </div>
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-6">
            <div className="flex items-center mb-6">
              <DollarSignIcon className="h-6 w-6 text-blue-400 mr-3" />
              <h3 className="text-xl font-semibold text-slate-100">Investment Details</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Investment Amount *
                </label>
                <select
                  value={formData.investmentAmount}
                  onChange={(e) => updateFormData("investmentAmount", e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select investment amount</option>
                  <option value="5000-10000">$5,000 - $10,000</option>
                  <option value="10000-25000">$10,000 - $25,000</option>
                  <option value="25000-50000">$25,000 - $50,000</option>
                  <option value="50000-100000">$50,000 - $100,000</option>
                  <option value="100000+">$100,000+</option>
                </select>
                {errors.investmentAmount && (
                  <p className="text-red-400 text-sm mt-1">{errors.investmentAmount}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Investment Type *
                </label>
                <select
                  value={formData.investmentType}
                  onChange={(e) => updateFormData("investmentType", e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select investment type</option>
                  <option value="equity">Equity Investment</option>
                  <option value="debt">Debt Investment</option>
                  <option value="convertible">Convertible Note</option>
                  <option value="safe">SAFE Agreement</option>
                </select>
                {errors.investmentType && (
                  <p className="text-red-400 text-sm mt-1">{errors.investmentType}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Risk Tolerance *
                </label>
                <select
                  value={formData.riskTolerance}
                  onChange={(e) => updateFormData("riskTolerance", e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select risk tolerance</option>
                  <option value="conservative">Conservative</option>
                  <option value="moderate">Moderate</option>
                  <option value="aggressive">Aggressive</option>
                  <option value="very-aggressive">Very Aggressive</option>
                </select>
                {errors.riskTolerance && (
                  <p className="text-red-400 text-sm mt-1">{errors.riskTolerance}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Investment Time Horizon *
                </label>
                <select
                  value={formData.timeHorizon}
                  onChange={(e) => updateFormData("timeHorizon", e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select time horizon</option>
                  <option value="1-2">1-2 years</option>
                  <option value="3-5">3-5 years</option>
                  <option value="5-10">5-10 years</option>
                  <option value="10+">10+ years</option>
                </select>
                {errors.timeHorizon && (
                  <p className="text-red-400 text-sm mt-1">{errors.timeHorizon}</p>
                )}
              </div>

              <div className="md:col-span-2">
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Investment Goals
                </label>
                <textarea
                  value={formData.investmentGoals}
                  onChange={(e) => updateFormData("investmentGoals", e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  placeholder="Describe your investment goals and objectives..."
                />
              </div>
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-6">
            <div className="flex items-center mb-6">
              <BuildingIcon className="h-6 w-6 text-blue-400 mr-3" />
              <h3 className="text-xl font-semibold text-slate-100">Financial Information</h3>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Annual Income *
                </label>
                <select
                  value={formData.annualIncome}
                  onChange={(e) => updateFormData("annualIncome", e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select annual income</option>
                  <option value="under-50k">Under $50,000</option>
                  <option value="50k-100k">$50,000 - $100,000</option>
                  <option value="100k-250k">$100,000 - $250,000</option>
                  <option value="250k-500k">$250,000 - $500,000</option>
                  <option value="500k+">$500,000+</option>
                </select>
                {errors.annualIncome && (
                  <p className="text-red-400 text-sm mt-1">{errors.annualIncome}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Net Worth *
                </label>
                <select
                  value={formData.netWorth}
                  onChange={(e) => updateFormData("netWorth", e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select net worth</option>
                  <option value="under-100k">Under $100,000</option>
                  <option value="100k-500k">$100,000 - $500,000</option>
                  <option value="500k-1m">$500,000 - $1,000,000</option>
                  <option value="1m-5m">$1,000,000 - $5,000,000</option>
                  <option value="5m+">$5,000,000+</option>
                </select>
                {errors.netWorth && (
                  <p className="text-red-400 text-sm mt-1">{errors.netWorth}</p>
                )}
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Liquid Assets
                </label>
                <select
                  value={formData.liquidAssets}
                  onChange={(e) => updateFormData("liquidAssets", e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select liquid assets</option>
                  <option value="under-25k">Under $25,000</option>
                  <option value="25k-100k">$25,000 - $100,000</option>
                  <option value="100k-500k">$100,000 - $500,000</option>
                  <option value="500k+">$500,000+</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  Investment Experience *
                </label>
                <select
                  value={formData.investmentExperience}
                  onChange={(e) => updateFormData("investmentExperience", e.target.value)}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="">Select experience level</option>
                  <option value="beginner">Beginner (0-2 years)</option>
                  <option value="intermediate">Intermediate (2-5 years)</option>
                  <option value="experienced">Experienced (5-10 years)</option>
                  <option value="expert">Expert (10+ years)</option>
                </select>
                {errors.investmentExperience && (
                  <p className="text-red-400 text-sm mt-1">{errors.investmentExperience}</p>
                )}
              </div>
            </div>
          </div>
        );

      case 4:
        return (
          <div className="space-y-6">
            <div className="flex items-center mb-6">
              <ShieldCheckIcon className="h-6 w-6 text-blue-400 mr-3" />
              <h3 className="text-xl font-semibold text-slate-100">Legal & Compliance</h3>
            </div>

            <div className="space-y-6">
              <div className="bg-slate-700 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="accredited"
                    checked={formData.accreditedInvestor}
                    onChange={(e) => updateFormData("accreditedInvestor", e.target.checked)}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-600 rounded"
                  />
                  <div>
                    <label htmlFor="accredited" className="text-sm font-medium text-slate-300">
                      Accredited Investor Status
                    </label>
                    <p className="text-xs text-slate-400 mt-1">
                      I certify that I am an accredited investor as defined by SEC regulations.
                    </p>
                  </div>
                </div>
              </div>

              <div className="bg-slate-700 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="terms"
                    checked={formData.agreeToTerms}
                    onChange={(e) => updateFormData("agreeToTerms", e.target.checked)}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-600 rounded"
                  />
                  <div>
                    <label htmlFor="terms" className="text-sm font-medium text-slate-300">
                      Terms and Conditions *
                    </label>
                    <p className="text-xs text-slate-400 mt-1">
                      I agree to the <a href="#" className="text-blue-400 hover:underline">Terms and Conditions</a> and understand the risks involved in this investment.
                    </p>
                  </div>
                </div>
                {errors.agreeToTerms && (
                  <p className="text-red-400 text-sm mt-2">{errors.agreeToTerms}</p>
                )}
              </div>

              <div className="bg-slate-700 p-4 rounded-lg">
                <div className="flex items-start space-x-3">
                  <input
                    type="checkbox"
                    id="privacy"
                    checked={formData.agreeToPrivacy}
                    onChange={(e) => updateFormData("agreeToPrivacy", e.target.checked)}
                    className="mt-1 h-4 w-4 text-blue-600 focus:ring-blue-500 border-slate-600 rounded"
                  />
                  <div>
                    <label htmlFor="privacy" className="text-sm font-medium text-slate-300">
                      Privacy Policy *
                    </label>
                    <p className="text-xs text-slate-400 mt-1">
                      I agree to the <a href="#" className="text-blue-400 hover:underline">Privacy Policy</a> and consent to the processing of my personal data.
                    </p>
                  </div>
                </div>
                {errors.agreeToPrivacy && (
                  <p className="text-red-400 text-sm mt-2">{errors.agreeToPrivacy}</p>
                )}
              </div>

              <div className="bg-slate-700 p-4 rounded-lg">
                <h4 className="text-sm font-medium text-slate-300 mb-3">Supporting Documents</h4>
                <div className="space-y-3">
                  <div>
                    <label className="block text-sm text-slate-400 mb-2">
                      Upload identity verification and financial documents
                    </label>
                    <input
                      type="file"
                      multiple
                      accept=".pdf,.jpg,.jpeg,.png,.doc,.docx"
                      onChange={handleFileUpload}
                      className="w-full px-3 py-2 bg-slate-600 border border-slate-500 rounded-md text-slate-100 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-600 file:text-white hover:file:bg-blue-700"
                    />
                  </div>

                  {formData.documents.length > 0 && (
                    <div className="space-y-2">
                      <p className="text-sm text-slate-300">Uploaded Documents:</p>
                      {formData.documents.map((file, index) => (
                        <div key={index} className="flex items-center justify-between bg-slate-600 p-2 rounded">
                          <span className="text-sm text-slate-300">{file.name}</span>
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removeDocument(index)}
                            className="border-slate-500 text-slate-300 hover:bg-slate-500"
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-100">Boss AI Investment Form</h1>
          <p className="text-slate-400 mt-2">
            Submit your investment application with complete documentation
          </p>
        </div>
        <Button
          onClick={() => setActiveView("investor-projects")}
          variant="outline"
          className="border-slate-600 text-slate-300 hover:bg-slate-700"
        >
          Back to Projects
        </Button>
      </div>

      {/* Progress Indicator */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="flex items-center justify-between mb-4">
            <h3 className="text-lg font-semibold text-slate-100">Application Progress</h3>
            <span className="text-sm text-slate-400">Step {currentStep} of {totalSteps}</span>
          </div>

          <div className="flex items-center space-x-4">
            {Array.from({ length: totalSteps }, (_, index) => {
              const stepNumber = index + 1;
              const isCompleted = stepNumber < currentStep;
              const isCurrent = stepNumber === currentStep;

              return (
                <React.Fragment key={stepNumber}>
                  <div className={`flex items-center justify-center w-8 h-8 rounded-full border-2 ${
                    isCompleted
                      ? 'bg-green-600 border-green-600 text-white'
                      : isCurrent
                        ? 'bg-blue-600 border-blue-600 text-white'
                        : 'border-slate-600 text-slate-400'
                  }`}>
                    {isCompleted ? (
                      <CheckCircleIcon className="h-5 w-5" />
                    ) : (
                      <span className="text-sm font-medium">{stepNumber}</span>
                    )}
                  </div>
                  {stepNumber < totalSteps && (
                    <div className={`flex-1 h-0.5 ${
                      isCompleted ? 'bg-green-600' : 'bg-slate-600'
                    }`} />
                  )}
                </React.Fragment>
              );
            })}
          </div>

          <div className="flex justify-between mt-4 text-sm">
            <span className={currentStep >= 1 ? 'text-slate-100' : 'text-slate-400'}>Personal Info</span>
            <span className={currentStep >= 2 ? 'text-slate-100' : 'text-slate-400'}>Investment Details</span>
            <span className={currentStep >= 3 ? 'text-slate-100' : 'text-slate-400'}>Financial Info</span>
            <span className={currentStep >= 4 ? 'text-slate-100' : 'text-slate-400'}>Legal & Compliance</span>
          </div>
        </CardContent>
      </Card>

      {/* Form Content */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-8">
          <motion.div
            key={currentStep}
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            {renderStepContent()}
          </motion.div>
        </CardContent>
      </Card>

      {/* Navigation Buttons */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="flex justify-between">
            <Button
              onClick={handlePrevious}
              disabled={currentStep === 1}
              variant="outline"
              className="border-slate-600 text-slate-300 hover:bg-slate-700 disabled:opacity-50"
            >
              Previous
            </Button>

            <div className="flex space-x-3">
              {currentStep < totalSteps ? (
                <Button
                  onClick={handleNext}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Next Step
                </Button>
              ) : (
                <Button
                  onClick={handleSubmit}
                  disabled={isSubmitting}
                  className="bg-green-600 hover:bg-green-700 text-white"
                >
                  {isSubmitting ? "Submitting..." : "Submit Application"}
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Important Notice */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="flex items-start space-x-3">
            <InfoIcon className="h-5 w-5 text-blue-400 mt-0.5" />
            <div>
              <h4 className="text-sm font-medium text-slate-100 mb-2">Important Notice</h4>
              <p className="text-sm text-slate-400">
                All investments carry risk and you may lose some or all of your investment.
                Please ensure you understand the risks before proceeding. This application
                will be reviewed by our investment team within 5-7 business days.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );
}
