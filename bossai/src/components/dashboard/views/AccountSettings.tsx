"use client";

import { DashboardPageWrapper } from "../DashboardLayout";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

export function AccountSettings() {
  return (
    <DashboardPageWrapper
      title="Account Settings"
      subtitle="User profile info, security settings, and preferences"
    >
      <Card>
        <CardHeader>
          <CardTitle>Account Settings</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500">Account settings view coming soon...</p>
        </CardContent>
      </Card>
    </DashboardPageWrapper>
  );
}
