"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  FileTextIcon,
  DownloadIcon,
  EyeOpenIcon,
  MagnifyingGlassIcon,
  DotsVerticalIcon,
  PlusIcon,
  CalendarIcon,
  PersonIcon,
  LockClosedIcon,
  Share1Icon
} from "@radix-ui/react-icons";

interface Document {
  id: string;
  name: string;
  type: 'contract' | 'report' | 'legal' | 'financial' | 'presentation' | 'other';
  size: string;
  uploadedAt: string;
  uploadedBy: string;
  status: 'active' | 'archived' | 'pending';
  isConfidential: boolean;
  projectId?: string;
  projectName?: string;
  url?: string;
}

export function DocumentsView() {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchDocuments();
  }, []);

  const fetchDocuments = async () => {
    try {
      setIsLoading(true);
      
      // Mock documents data - in real app, fetch from API
      const mockDocuments: Document[] = [
        {
          id: "1",
          name: "Investment Agreement - AI Healthcare Platform",
          type: "contract",
          size: "2.4 MB",
          uploadedAt: "2024-01-15T10:30:00Z",
          uploadedBy: "John Smith",
          status: "active",
          isConfidential: true,
          projectId: "proj-1",
          projectName: "AI-Powered Healthcare Platform"
        },
        {
          id: "2",
          name: "Q4 2023 Portfolio Performance Report",
          type: "report",
          size: "1.8 MB",
          uploadedAt: "2024-01-10T14:20:00Z",
          uploadedBy: "Sarah Johnson",
          status: "active",
          isConfidential: false
        },
        {
          id: "3",
          name: "Due Diligence - Green Energy Storage",
          type: "legal",
          size: "5.2 MB",
          uploadedAt: "2024-01-08T09:15:00Z",
          uploadedBy: "Michael Chen",
          status: "active",
          isConfidential: true,
          projectId: "proj-2",
          projectName: "Green Energy Storage Solutions"
        },
        {
          id: "4",
          name: "Financial Projections 2024-2026",
          type: "financial",
          size: "892 KB",
          uploadedAt: "2024-01-05T16:45:00Z",
          uploadedBy: "Emily Davis",
          status: "active",
          isConfidential: false
        },
        {
          id: "5",
          name: "Investor Presentation - Q1 2024",
          type: "presentation",
          size: "12.3 MB",
          uploadedAt: "2024-01-03T11:30:00Z",
          uploadedBy: "David Wilson",
          status: "active",
          isConfidential: false
        },
        {
          id: "6",
          name: "Compliance Audit Report",
          type: "legal",
          size: "3.1 MB",
          uploadedAt: "2023-12-28T13:20:00Z",
          uploadedBy: "Lisa Anderson",
          status: "archived",
          isConfidential: true
        }
      ];

      setDocuments(mockDocuments);
    } catch (error) {
      console.error('Error fetching documents:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.projectName?.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === "all" || doc.type === selectedType;
    return matchesSearch && matchesType;
  });

  const getTypeColor = (type: string) => {
    const colors = {
      contract: "bg-blue-500/20 text-blue-400",
      report: "bg-green-500/20 text-green-400",
      legal: "bg-red-500/20 text-red-400",
      financial: "bg-yellow-500/20 text-yellow-400",
      presentation: "bg-purple-500/20 text-purple-400",
      other: "bg-gray-500/20 text-gray-400"
    };
    return colors[type as keyof typeof colors] || colors.other;
  };

  const getStatusColor = (status: string) => {
    const colors = {
      active: "bg-green-500/20 text-green-400",
      archived: "bg-gray-500/20 text-gray-400",
      pending: "bg-yellow-500/20 text-yellow-400"
    };
    return colors[status as keyof typeof colors] || colors.pending;
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  };

  const handleDownload = (document: Document) => {
    // Mock download functionality
    console.log(`Downloading document: ${document.name}`);
    // In real app, would trigger actual download
  };

  const handleView = (document: Document) => {
    // Mock view functionality
    console.log(`Viewing document: ${document.name}`);
    // In real app, would open document viewer
  };

  const handleShare = (document: Document) => {
    // Mock share functionality
    console.log(`Sharing document: ${document.name}`);
    // In real app, would open share dialog
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white">Document Management</h1>
          <p className="text-slate-400">Secure storage and management of investment documents</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <PlusIcon className="mr-2 h-4 w-4" />
          Upload Document
        </Button>
      </div>

      {/* Search and Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            placeholder="Search documents..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="pl-10 bg-slate-800 border-slate-700 text-white"
          />
        </div>
        <Tabs value={selectedType} onValueChange={setSelectedType} className="w-auto">
          <TabsList className="bg-slate-800 border-slate-700">
            <TabsTrigger value="all">All</TabsTrigger>
            <TabsTrigger value="contract">Contracts</TabsTrigger>
            <TabsTrigger value="report">Reports</TabsTrigger>
            <TabsTrigger value="legal">Legal</TabsTrigger>
            <TabsTrigger value="financial">Financial</TabsTrigger>
            <TabsTrigger value="presentation">Presentations</TabsTrigger>
          </TabsList>
        </Tabs>
      </div>

      {/* Document Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <FileTextIcon className="h-8 w-8 text-blue-400" />
              <div>
                <p className="text-2xl font-bold text-white">{documents.length}</p>
                <p className="text-sm text-slate-400">Total Documents</p>
              </div>
            </div>
          </CardContent>
        </Card>
        
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <LockClosedIcon className="h-8 w-8 text-red-400" />
              <div>
                <p className="text-2xl font-bold text-white">
                  {documents.filter(d => d.isConfidential).length}
                </p>
                <p className="text-sm text-slate-400">Confidential</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <CalendarIcon className="h-8 w-8 text-green-400" />
              <div>
                <p className="text-2xl font-bold text-white">
                  {documents.filter(d => d.status === 'active').length}
                </p>
                <p className="text-sm text-slate-400">Active</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <Share1Icon className="h-8 w-8 text-purple-400" />
              <div>
                <p className="text-2xl font-bold text-white">
                  {Math.round(documents.reduce((sum, d) => sum + parseFloat(d.size), 0) * 100) / 100}
                </p>
                <p className="text-sm text-slate-400">Total Size (MB)</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Documents List */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="text-white">Documents</CardTitle>
          <CardDescription>
            {filteredDocuments.length} of {documents.length} documents
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredDocuments.map((document) => (
              <div key={document.id} className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg hover:bg-slate-700/50 transition-colors">
                <div className="flex items-center gap-4 flex-1">
                  <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center">
                    <FileTextIcon className="h-6 w-6 text-blue-400" />
                  </div>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-2 mb-1">
                      <h3 className="font-medium text-white truncate">{document.name}</h3>
                      {document.isConfidential && (
                        <LockClosedIcon className="h-4 w-4 text-red-400" />
                      )}
                    </div>
                    
                    <div className="flex items-center gap-4 text-sm text-slate-400">
                      <span>{document.size}</span>
                      <span>•</span>
                      <span>Uploaded {formatDate(document.uploadedAt)}</span>
                      <span>•</span>
                      <span>By {document.uploadedBy}</span>
                      {document.projectName && (
                        <>
                          <span>•</span>
                          <span className="text-blue-400">{document.projectName}</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-3">
                  <Badge className={getTypeColor(document.type)}>
                    {document.type}
                  </Badge>
                  <Badge className={getStatusColor(document.status)}>
                    {document.status}
                  </Badge>
                  
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                        <DotsVerticalIcon className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end" className="bg-slate-800 border-slate-700">
                      <DropdownMenuItem onClick={() => handleView(document)} className="text-white hover:bg-slate-700">
                        <EyeOpenIcon className="mr-2 h-4 w-4" />
                        View
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleDownload(document)} className="text-white hover:bg-slate-700">
                        <DownloadIcon className="mr-2 h-4 w-4" />
                        Download
                      </DropdownMenuItem>
                      <DropdownMenuItem onClick={() => handleShare(document)} className="text-white hover:bg-slate-700">
                        <Share1Icon className="mr-2 h-4 w-4" />
                        Share
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
