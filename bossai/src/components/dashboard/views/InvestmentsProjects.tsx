"use client";

import React, { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  TrendingUpIcon,
  DollarSignIcon,
  CalendarIcon,
  UsersIcon,
  StarIcon,
  FilterIcon,
  SearchIcon,
  ChevronDownIcon,
  ExternalLinkIcon,
  InfoIcon
} from "lucide-react";
import { ActiveView } from "@/contexts/DashboardContext";

interface InvestmentsProjectsProps {
  setActiveView: (view: ActiveView) => void;
}

interface Project {
  id: string;
  title: string;
  description: string;
  category: string;
  status: "Active" | "Funding" | "Pre-Launch" | "Completed";
  targetRaise: number;
  minInvestment: number;
  raised: number;
  investors: number;
  rating: number;
  daysLeft: number;
  riskLevel: "Low" | "Medium" | "High";
  growthPotential: "Stable" | "High Growth" | "High Potential";
  featured: boolean;
}

export function InvestmentsProjects({ setActiveView }: InvestmentsProjectsProps) {
  const [projects, setProjects] = useState<Project[]>([]);
  const [filteredProjects, setFilteredProjects] = useState<Project[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState("All Categories");
  const [selectedRisk, setSelectedRisk] = useState("All Risk Levels");
  const [selectedStatus, setSelectedStatus] = useState("All Status");
  const [showFilters, setShowFilters] = useState(false);

  const categories = ["All Categories", ...Array.from(new Set(projects.map(p => p.category)))];
  const riskLevels = ["All Risk Levels", "CONSERVATIVE", "MODERATE", "AGGRESSIVE", "VERY_AGGRESSIVE"];
  const statuses = ["All Status", "DRAFT", "ACTIVE", "FUNDING", "FUNDED", "COMPLETED"];

  // Fetch projects from API
  useEffect(() => {
    const fetchProjects = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/projects');
        if (response.ok) {
          const data = await response.json();
          const transformedProjects = data.projects.map((project: any) => ({
            id: project.id,
            title: project.title,
            description: project.description,
            category: project.sector, // Map sector to category
            status: project.status,
            targetRaise: project.fundingGoal,
            minInvestment: project.minInvestment,
            raised: project.currentFunding,
            investors: project.investorCount || 0,
            rating: 4.5, // Default rating - could be calculated from reviews
            daysLeft: project.endDate ? Math.max(0, Math.ceil((new Date(project.endDate).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24))) : 30,
            riskLevel: project.riskLevel,
            growthPotential: project.roiEstimate > 20 ? "High Growth" : project.roiEstimate > 10 ? "Stable" : "Conservative",
            featured: project.featured
          }));
          setProjects(transformedProjects);
          setFilteredProjects(transformedProjects);
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  useEffect(() => {
    let filtered = projects;

    // Search filter
    if (searchTerm) {
      filtered = filtered.filter(project =>
        project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        project.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Category filter
    if (selectedCategory !== "All Categories") {
      filtered = filtered.filter(project => project.category === selectedCategory);
    }

    // Risk filter
    if (selectedRisk !== "All Risk Levels") {
      filtered = filtered.filter(project => project.riskLevel === selectedRisk);
    }

    // Status filter
    if (selectedStatus !== "All Status") {
      filtered = filtered.filter(project => project.status === selectedStatus);
    }

    setFilteredProjects(filtered);
  }, [searchTerm, selectedCategory, selectedRisk, selectedStatus, projects]);

  const formatCurrency = (amount: number) => {
    if (amount >= 1000000) {
      return `$${(amount / 1000000).toFixed(1)}M`;
    } else if (amount >= 1000) {
      return `$${(amount / 1000).toFixed(0)}K`;
    }
    return `$${amount.toLocaleString()}`;
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "bg-green-600";
      case "Funding": return "bg-blue-600";
      case "Pre-Launch": return "bg-purple-600";
      case "Completed": return "bg-gray-600";
      default: return "bg-gray-600";
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case "Low": return "text-green-400";
      case "Medium": return "text-yellow-400";
      case "High": return "text-red-400";
      default: return "text-slate-400";
    }
  };

  const getProgressColor = (percentage: number) => {
    if (percentage >= 75) return "bg-green-500";
    if (percentage >= 50) return "bg-blue-500";
    if (percentage >= 25) return "bg-yellow-500";
    return "bg-purple-500";
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-slate-100">Investment Projects</h1>
          <p className="text-slate-400 mt-2">
            Discover and invest in cutting-edge AI and technology ventures
          </p>
        </div>
        <Button
          onClick={() => setActiveView("investor-boss-ai-form")}
          className="bg-blue-600 hover:bg-blue-700 text-white"
        >
          New Investment
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Total Projects</p>
                <p className="text-2xl font-bold text-slate-100">{projects.length}</p>
              </div>
              <TrendingUpIcon className="h-8 w-8 text-blue-400" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Active Funding</p>
                <p className="text-2xl font-bold text-slate-100">{projects.filter(p => p.status === "Active" || p.status === "Funding").length}</p>
              </div>
              <DollarSignIcon className="h-8 w-8 text-green-400" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Total Raised</p>
                <p className="text-2xl font-bold text-slate-100">{formatCurrency(projects.reduce((sum, p) => sum + p.raised, 0))}</p>
              </div>
              <UsersIcon className="h-8 w-8 text-purple-400" />
            </div>
          </CardContent>
        </Card>
        <Card className="bg-slate-800 border-slate-700">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-slate-400 text-sm">Avg Rating</p>
                <p className="text-2xl font-bold text-slate-100">{(projects.reduce((sum, p) => sum + p.rating, 0) / projects.length).toFixed(1)}</p>
              </div>
              <StarIcon className="h-8 w-8 text-yellow-400" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters and Search */}
      <Card className="bg-slate-800 border-slate-700">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4 items-center">
            <div className="flex-1 relative">
              <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
              <input
                type="text"
                placeholder="Search projects..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 placeholder-slate-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            <div className="flex gap-2">
              <select
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
                className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {categories.map(category => (
                  <option key={category} value={category}>{category}</option>
                ))}
              </select>
              <select
                value={selectedRisk}
                onChange={(e) => setSelectedRisk(e.target.value)}
                className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {riskLevels.map(risk => (
                  <option key={risk} value={risk}>{risk}</option>
                ))}
              </select>
              <select
                value={selectedStatus}
                onChange={(e) => setSelectedStatus(e.target.value)}
                className="px-3 py-2 bg-slate-700 border border-slate-600 rounded-md text-slate-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                {statuses.map(status => (
                  <option key={status} value={status}>{status}</option>
                ))}
              </select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Investment Projects Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {loading ? (
          // Loading skeleton
          Array.from({ length: 6 }).map((_, i) => (
            <Card key={i} className="bg-slate-800 border-slate-700">
              <CardHeader>
                <div className="animate-pulse">
                  <div className="h-4 bg-slate-700 rounded w-20 mb-2"></div>
                  <div className="h-6 bg-slate-700 rounded w-3/4 mb-2"></div>
                  <div className="h-4 bg-slate-700 rounded w-full"></div>
                </div>
              </CardHeader>
              <CardContent>
                <div className="animate-pulse space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="h-4 bg-slate-700 rounded"></div>
                    <div className="h-4 bg-slate-700 rounded"></div>
                  </div>
                  <div className="h-2 bg-slate-700 rounded"></div>
                  <div className="h-8 bg-slate-700 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))
        ) : filteredProjects.length === 0 ? (
          <div className="col-span-full text-center py-12">
            <p className="text-slate-400 text-lg">No investment projects found</p>
            <p className="text-slate-500 text-sm mt-2">Try adjusting your filters or search terms</p>
          </div>
        ) : (
          filteredProjects.map((project) => {
          const progressPercentage = (project.raised / project.targetRaise) * 100;

          return (
            <Card key={project.id} className="bg-slate-800 border-slate-700 hover:border-blue-500 transition-colors">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <Badge className={`${getStatusColor(project.status)} text-white`}>
                    {project.status}
                  </Badge>
                  <div className="flex items-center text-yellow-400">
                    <StarIcon className="h-4 w-4 fill-current" />
                    <span className="ml-1 text-sm">{project.rating}</span>
                  </div>
                </div>
                <CardTitle className="text-slate-100">{project.title}</CardTitle>
                <CardDescription className="text-slate-400">
                  {project.description}
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <p className="text-slate-400">Target Raise</p>
                      <p className="text-slate-100 font-semibold">{formatCurrency(project.targetRaise)}</p>
                    </div>
                    <div>
                      <p className="text-slate-400">Min Investment</p>
                      <p className="text-slate-100 font-semibold">{formatCurrency(project.minInvestment)}</p>
                    </div>
                    <div>
                      <p className="text-slate-400">Raised</p>
                      <p className="text-green-400 font-semibold">
                        {formatCurrency(project.raised)} ({Math.round(progressPercentage)}%)
                      </p>
                    </div>
                    <div>
                      <p className="text-slate-400">Investors</p>
                      <p className="text-slate-100 font-semibold">{project.investors}</p>
                    </div>
                  </div>

                  <div className="w-full bg-slate-700 rounded-full h-2">
                    <div
                      className={`${getProgressColor(progressPercentage)} h-2 rounded-full transition-all duration-300`}
                      style={{ width: `${Math.min(progressPercentage, 100)}%` }}
                    ></div>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center text-slate-400">
                      <CalendarIcon className="h-4 w-4 mr-1" />
                      <span>{project.daysLeft} days left</span>
                    </div>
                    <div className="flex items-center">
                      <span className={`text-sm ${getRiskColor(project.riskLevel)}`}>
                        {project.riskLevel} Risk
                      </span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div className="flex items-center">
                      <TrendingUpIcon className="h-4 w-4 mr-1 text-blue-400" />
                      <span className="text-blue-400">{project.growthPotential}</span>
                    </div>
                    <Badge variant="outline" className="text-xs border-slate-600 text-slate-300">
                      {project.category}
                    </Badge>
                  </div>

                  <div className="flex gap-2">
                    <Button
                      className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
                      onClick={() => setActiveView("investor-boss-ai-form")}
                    >
                      Invest Now
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      className="border-slate-600 text-slate-300 hover:bg-slate-700"
                    >
                      <InfoIcon className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          );
        }))}
      </div>



      {/* Load More */}
      {filteredProjects.length > 0 && (
        <div className="text-center">
          <Button variant="outline" className="border-slate-600 text-slate-300 hover:bg-slate-700">
            Load More Projects
          </Button>
        </div>
      )}
    </motion.div>
  );
}
