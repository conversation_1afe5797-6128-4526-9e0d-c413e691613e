"use client";

import { DashboardPageWrapper } from "../DashboardLayout";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

export function PortfolioAnalytics() {
  return (
    <DashboardPageWrapper
      title="Portfolio Analytics"
      subtitle="Interactive charts and tables for investment performance"
    >
      <Card>
        <CardHeader>
          <CardTitle>Portfolio Analytics</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500">Portfolio analytics view coming soon...</p>
        </CardContent>
      </Card>
    </DashboardPageWrapper>
  );
}
