"use client";

import { DashboardPageWrapper } from "../DashboardLayout";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

export function DocumentsKYC() {
  return (
    <DashboardPageWrapper
      title="Documents / KYC"
      subtitle="Upload, view, and manage legal documents"
    >
      <Card>
        <CardHeader>
          <CardTitle>Documents & KYC</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500">Documents & KYC view coming soon...</p>
        </CardContent>
      </Card>
    </DashboardPageWrapper>
  );
}
