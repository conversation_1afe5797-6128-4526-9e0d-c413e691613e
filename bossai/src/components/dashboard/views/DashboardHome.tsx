"use client";

import { motion } from "framer-motion";
import { useDashboard } from "@/contexts/DashboardContext";
import { DashboardPageWrapper } from "../DashboardLayout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  TrendingUpIcon, 
  DollarSignIcon, 
  BarChart3Icon, 
  ClockIcon,
  ArrowUpIcon,
  ArrowDownIcon,
  PlusIcon
} from "lucide-react";

export function DashboardHome() {
  const { state, setActiveView } = useDashboard();
  const { user, portfolioStats, investments } = state;

  // Calculate recent activity
  const recentInvestments = investments
    .sort((a, b) => new Date(b.submittedAt).getTime() - new Date(a.submittedAt).getTime())
    .slice(0, 5);

  // Mock data for sparkline (in real app, this would come from API)
  const portfolioGrowthData = [65, 68, 72, 69, 75, 78, 82, 85, 88, 92, 89, 95];

  const quickActions = [
    {
      title: "New Investment",
      description: "Submit investment application",
      action: () => setActiveView("investor-boss-ai-form"),
      icon: PlusIcon,
      color: "bg-blue-600 hover:bg-blue-700"
    },
    {
      title: "Upload Documents",
      description: "Complete KYC verification",
      action: () => setActiveView("investor-documents"),
      icon: BarChart3Icon,
      color: "bg-purple-600 hover:bg-purple-700"
    },
    {
      title: "View Analytics",
      description: "Detailed portfolio analysis",
      action: () => setActiveView("investor-analytics"),
      icon: TrendingUpIcon,
      color: "bg-green-600 hover:bg-green-700"
    }
  ];

  return (
    <DashboardPageWrapper
      title="Dashboard / Home"
      subtitle="Your investment portfolio overview"
    >
      {/* Welcome Section */}
      <Card className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-2xl font-bold mb-2">
                Welcome back, {user?.firstName || user?.name || "Investor"}!
              </h2>
              <p className="text-blue-100">
                Here's your investment portfolio overview
              </p>
            </div>
            <div className="text-right">
              <p className="text-sm text-blue-100">KYC Status</p>
              <Badge 
                variant="secondary" 
                className="bg-white/20 text-white border-white/30"
              >
                {user?.kycStatus?.replace('_', ' ') || 'Not Started'}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Portfolio Metrics */}
      {portfolioStats && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Invested</CardTitle>
              <DollarSignIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">
                $0
              </div>
              <p className="text-xs text-muted-foreground">
                +0% from last month
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
              <BarChart3Icon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                0
              </div>
              <p className="text-xs text-muted-foreground">
                0 pending approval
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Value</CardTitle>
              <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-blue-600">
                $0
              </div>
              <div className="flex items-center text-xs text-green-600">
                <ArrowUpIcon className="h-3 w-3 mr-1" />
                +0.0% growth
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Returns</CardTitle>
              <TrendingUpIcon className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-purple-600">
                +$0
              </div>
              <p className="text-xs text-muted-foreground">
                0.0% average return
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Portfolio Growth Chart & Quick Actions */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Mini Portfolio Growth Graph */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingUpIcon className="h-5 w-5" />
              <span>Portfolio Growth</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600">Last 12 months</span>
                <span className="text-sm font-semibold text-green-600">
                  +{portfolioStats?.portfolioGrowth.toFixed(1)}%
                </span>
              </div>
              
              {/* Simple Sparkline */}
              <div className="h-20 flex items-end space-x-1">
                {portfolioGrowthData.map((value, index) => (
                  <motion.div
                    key={index}
                    initial={{ height: 0 }}
                    animate={{ height: `${(value / 100) * 100}%` }}
                    transition={{ delay: index * 0.1 }}
                    className="flex-1 bg-gradient-to-t from-blue-600 to-purple-600 rounded-t-sm min-h-[4px]"
                  />
                ))}
              </div>
              
              <div className="flex justify-between text-xs text-gray-500">
                <span>Jan</span>
                <span>Dec</span>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {quickActions.map((action, index) => (
              <motion.div
                key={action.title}
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Button
                  onClick={action.action}
                  variant="outline"
                  className="w-full justify-start h-auto p-4 hover:bg-gray-50"
                >
                  <div className={`p-2 rounded-lg ${action.color} mr-3`}>
                    <action.icon className="h-4 w-4 text-white" />
                  </div>
                  <div className="text-left">
                    <div className="font-medium">{action.title}</div>
                    <div className="text-sm text-gray-500">{action.description}</div>
                  </div>
                </Button>
              </motion.div>
            ))}
          </CardContent>
        </Card>
      </div>

      {/* Recent Activity */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <ClockIcon className="h-5 w-5" />
            <span>Recent Activity</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {recentInvestments.length > 0 ? (
            <div className="space-y-4">
              {recentInvestments.map((investment, index) => (
                <motion.div
                  key={investment.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-3 h-3 rounded-full ${
                      investment.status === 'APPROVED' ? 'bg-green-500' :
                      investment.status === 'PENDING' ? 'bg-yellow-500' :
                      investment.status === 'ACTIVE' ? 'bg-blue-500' :
                      'bg-gray-500'
                    }`} />
                    <div>
                      <p className="font-medium">{investment.type} Investment</p>
                      <p className="text-sm text-gray-500">
                        {new Date(investment.submittedAt).toLocaleDateString()}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <p className="font-semibold">${investment.amount.toLocaleString()}</p>
                    <Badge variant="outline" className="text-xs">
                      {investment.status}
                    </Badge>
                  </div>
                </motion.div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <p className="text-gray-500">No recent activity</p>
              <Button 
                onClick={() => setActiveView("investor-boss-ai-form")}
                className="mt-4"
              >
                Make Your First Investment
              </Button>
            </div>
          )}
        </CardContent>
      </Card>
    </DashboardPageWrapper>
  );
}
