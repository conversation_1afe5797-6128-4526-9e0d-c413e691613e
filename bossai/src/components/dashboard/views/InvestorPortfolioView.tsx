"use client";

import React, { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  BarChartIcon,
  TriangleUpIcon as TrendingUpIcon,
  TriangleDownIcon as TrendingDownIcon,
  CalendarIcon,
  DownloadIcon,
  EyeOpenIcon,
  PieChartIcon,
  ActivityLogIcon,
  TokensIcon as DollarSignIcon
} from "@radix-ui/react-icons";

interface Investment {
  id: string;
  projectName: string;
  amount: number;
  currentValue: number;
  returns: number;
  returnsPercentage: number;
  status: "active" | "completed" | "pending";
  investmentDate: string;
  sector: string;
  riskLevel: "low" | "medium" | "high";
}

interface PortfolioMetrics {
  totalInvested: number;
  currentValue: number;
  totalReturns: number;
  returnsPercentage: number;
  activeInvestments: number;
  completedInvestments: number;
  portfolioGrowth: number;
}

export function InvestorPortfolioView() {
  const [portfolioMetrics, setPortfolioMetrics] = useState<PortfolioMetrics | null>(null);
  const [investments, setInvestments] = useState<Investment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchPortfolioData();
  }, []);

  const fetchPortfolioData = async () => {
    try {
      // Fetch portfolio stats
      const statsResponse = await fetch("/api/portfolio/stats");
      const statsData = await statsResponse.json();
      
      // Fetch investments
      const investmentsResponse = await fetch("/api/investments");
      const investmentsData = await investmentsResponse.json();

      // Mock enhanced portfolio metrics for investor view
      const enhancedMetrics: PortfolioMetrics = {
        totalInvested: statsData.totalInvested || 250000,
        currentValue: statsData.totalValue || 287500,
        totalReturns: statsData.totalReturns || 37500,
        returnsPercentage: 15.0,
        activeInvestments: investmentsData.filter((inv: any) => inv.status === "active").length || 5,
        completedInvestments: investmentsData.filter((inv: any) => inv.status === "completed").length || 2,
        portfolioGrowth: 12.5
      };

      // Mock enhanced investments data
      const enhancedInvestments: Investment[] = [
        {
          id: "1",
          projectName: "AI Healthcare Platform",
          amount: 50000,
          currentValue: 62500,
          returns: 12500,
          returnsPercentage: 25.0,
          status: "active",
          investmentDate: "2024-01-15",
          sector: "Healthcare",
          riskLevel: "medium"
        },
        {
          id: "2", 
          projectName: "Green Energy Storage",
          amount: 75000,
          currentValue: 82500,
          returns: 7500,
          returnsPercentage: 10.0,
          status: "active",
          investmentDate: "2024-02-20",
          sector: "Energy",
          riskLevel: "low"
        },
        {
          id: "3",
          projectName: "Fintech Innovation",
          amount: 40000,
          currentValue: 48000,
          returns: 8000,
          returnsPercentage: 20.0,
          status: "active",
          investmentDate: "2024-03-10",
          sector: "Fintech",
          riskLevel: "high"
        },
        {
          id: "4",
          projectName: "Smart City Infrastructure",
          amount: 60000,
          currentValue: 69000,
          returns: 9000,
          returnsPercentage: 15.0,
          status: "active",
          investmentDate: "2024-01-30",
          sector: "Infrastructure",
          riskLevel: "medium"
        },
        {
          id: "5",
          projectName: "EdTech Platform",
          amount: 25000,
          currentValue: 25500,
          returns: 500,
          returnsPercentage: 2.0,
          status: "completed",
          investmentDate: "2023-11-15",
          sector: "Education",
          riskLevel: "low"
        }
      ];

      setPortfolioMetrics(enhancedMetrics);
      setInvestments(enhancedInvestments);
    } catch (error) {
      console.error("Error fetching portfolio data:", error);
    } finally {
      setLoading(false);
    }
  };

  const getRiskBadgeColor = (risk: string) => {
    switch (risk) {
      case "low": return "bg-green-100 text-green-800";
      case "medium": return "bg-yellow-100 text-yellow-800";
      case "high": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "active": return "bg-blue-100 text-blue-800";
      case "completed": return "bg-green-100 text-green-800";
      case "pending": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-900">Portfolio Analytics</h1>
          <p className="text-slate-600 mt-1">Track your investment performance and portfolio growth</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <DownloadIcon className="w-4 h-4 mr-2" />
          Export Report
        </Button>
      </div>

      {/* Portfolio Overview Cards */}
      {portfolioMetrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Invested</CardTitle>
              <DollarSignIcon className="h-4 w-4 text-slate-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${portfolioMetrics.totalInvested.toLocaleString()}</div>
              <p className="text-xs text-slate-600">Across {portfolioMetrics.activeInvestments} active investments</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Current Value</CardTitle>
              <TrendingUpIcon className="h-4 w-4 text-green-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">${portfolioMetrics.currentValue.toLocaleString()}</div>
              <p className="text-xs text-green-600">+{portfolioMetrics.portfolioGrowth}% growth</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Returns</CardTitle>
              <BarChartIcon className="h-4 w-4 text-blue-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold text-green-600">+${portfolioMetrics.totalReturns.toLocaleString()}</div>
              <p className="text-xs text-slate-600">{portfolioMetrics.returnsPercentage}% average return</p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Investments</CardTitle>
              <ActivityLogIcon className="h-4 w-4 text-purple-600" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{portfolioMetrics.activeInvestments}</div>
              <p className="text-xs text-slate-600">{portfolioMetrics.completedInvestments} completed</p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Portfolio Details */}
      <Tabs defaultValue="investments" className="space-y-4">
        <TabsList>
          <TabsTrigger value="investments">My Investments</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="allocation">Asset Allocation</TabsTrigger>
        </TabsList>

        <TabsContent value="investments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Investment Portfolio</CardTitle>
              <CardDescription>Your current investment positions and performance</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {investments.map((investment) => (
                  <div key={investment.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        <h3 className="font-semibold">{investment.projectName}</h3>
                        <Badge className={getStatusBadgeColor(investment.status)}>
                          {investment.status}
                        </Badge>
                        <Badge className={getRiskBadgeColor(investment.riskLevel)}>
                          {investment.riskLevel} risk
                        </Badge>
                      </div>
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                        <div>
                          <p className="text-slate-600">Invested</p>
                          <p className="font-medium">${investment.amount.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-slate-600">Current Value</p>
                          <p className="font-medium">${investment.currentValue.toLocaleString()}</p>
                        </div>
                        <div>
                          <p className="text-slate-600">Returns</p>
                          <p className={`font-medium ${investment.returns >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                            {investment.returns >= 0 ? '+' : ''}${investment.returns.toLocaleString()} ({investment.returnsPercentage}%)
                          </p>
                        </div>
                        <div>
                          <p className="text-slate-600">Sector</p>
                          <p className="font-medium">{investment.sector}</p>
                        </div>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      <EyeOpenIcon className="w-4 h-4 mr-2" />
                      View Details
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Performance Analytics</CardTitle>
              <CardDescription>Detailed performance metrics and trends</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">Portfolio Growth</h4>
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>1 Month</span>
                      <span className="text-green-600">+2.3%</span>
                    </div>
                    <Progress value={23} className="h-2" />
                    <div className="flex justify-between text-sm">
                      <span>3 Months</span>
                      <span className="text-green-600">+7.8%</span>
                    </div>
                    <Progress value={78} className="h-2" />
                    <div className="flex justify-between text-sm">
                      <span>6 Months</span>
                      <span className="text-green-600">+12.5%</span>
                    </div>
                    <Progress value={85} className="h-2" />
                    <div className="flex justify-between text-sm">
                      <span>1 Year</span>
                      <span className="text-green-600">+15.0%</span>
                    </div>
                    <Progress value={90} className="h-2" />
                  </div>
                </div>
                <div className="space-y-4">
                  <h4 className="font-semibold">Risk Metrics</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Portfolio Beta</span>
                      <span className="font-medium">0.85</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Sharpe Ratio</span>
                      <span className="font-medium">1.42</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Max Drawdown</span>
                      <span className="font-medium text-red-600">-3.2%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Volatility</span>
                      <span className="font-medium">8.7%</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="allocation" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Asset Allocation</CardTitle>
              <CardDescription>Breakdown of your investment portfolio by sector and risk</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h4 className="font-semibold">By Sector</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Healthcare</span>
                      <div className="flex items-center gap-2">
                        <Progress value={25} className="w-20 h-2" />
                        <span className="text-sm font-medium">25%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Energy</span>
                      <div className="flex items-center gap-2">
                        <Progress value={30} className="w-20 h-2" />
                        <span className="text-sm font-medium">30%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Fintech</span>
                      <div className="flex items-center gap-2">
                        <Progress value={20} className="w-20 h-2" />
                        <span className="text-sm font-medium">20%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Infrastructure</span>
                      <div className="flex items-center gap-2">
                        <Progress value={15} className="w-20 h-2" />
                        <span className="text-sm font-medium">15%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Education</span>
                      <div className="flex items-center gap-2">
                        <Progress value={10} className="w-20 h-2" />
                        <span className="text-sm font-medium">10%</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <h4 className="font-semibold">By Risk Level</h4>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Low Risk</span>
                      <div className="flex items-center gap-2">
                        <Progress value={40} className="w-20 h-2" />
                        <span className="text-sm font-medium">40%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">Medium Risk</span>
                      <div className="flex items-center gap-2">
                        <Progress value={44} className="w-20 h-2" />
                        <span className="text-sm font-medium">44%</span>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm">High Risk</span>
                      <div className="flex items-center gap-2">
                        <Progress value={16} className="w-20 h-2" />
                        <span className="text-sm font-medium">16%</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
