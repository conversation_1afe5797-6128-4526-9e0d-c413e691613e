"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Tabs, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  FileTextIcon,
  DownloadIcon,
  EyeOpenIcon,
  UploadIcon,
  CheckCircledIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
  DotsVerticalIcon,
  PersonIcon,
  IdCardIcon,
  CameraIcon
} from "@radix-ui/react-icons";

interface Document {
  id: string;
  name: string;
  type: "kyc" | "investment" | "legal" | "tax" | "identity";
  status: "approved" | "pending" | "rejected" | "required";
  uploadDate: string;
  size: string;
  description: string;
}

interface KYCStatus {
  overall: "complete" | "pending" | "incomplete";
  identity: "approved" | "pending" | "required";
  address: "approved" | "pending" | "required";
  income: "approved" | "pending" | "required";
  accreditation: "approved" | "pending" | "required";
}

export function InvestorDocumentsView() {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState<string>("all");

  // Mock KYC status
  const kycStatus: KYCStatus = {
    overall: "pending",
    identity: "approved",
    address: "pending",
    income: "approved",
    accreditation: "required"
  };

  // Mock documents data
  const documents: Document[] = [
    {
      id: "1",
      name: "Driver's License",
      type: "identity",
      status: "approved",
      uploadDate: "2024-01-15",
      size: "2.3 MB",
      description: "Government-issued photo ID for identity verification"
    },
    {
      id: "2",
      name: "Proof of Address",
      type: "kyc",
      status: "pending",
      uploadDate: "2024-01-20",
      size: "1.8 MB",
      description: "Utility bill for address verification"
    },
    {
      id: "3",
      name: "Income Statement",
      type: "kyc",
      status: "approved",
      uploadDate: "2024-01-18",
      size: "3.1 MB",
      description: "Annual income verification document"
    },
    {
      id: "4",
      name: "Investment Agreement - AI Healthcare",
      type: "investment",
      status: "approved",
      uploadDate: "2024-01-25",
      size: "4.2 MB",
      description: "Signed investment agreement for AI Healthcare Platform"
    },
    {
      id: "5",
      name: "Accredited Investor Certificate",
      type: "kyc",
      status: "required",
      uploadDate: "",
      size: "",
      description: "Certificate proving accredited investor status"
    },
    {
      id: "6",
      name: "Tax Form W-9",
      type: "tax",
      status: "approved",
      uploadDate: "2024-01-22",
      size: "1.2 MB",
      description: "Tax identification and certification form"
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "approved": return <CheckCircledIcon className="w-4 h-4 text-green-600" />;
      case "pending": return <ClockIcon className="w-4 h-4 text-yellow-600" />;
      case "rejected": return <ExclamationTriangleIcon className="w-4 h-4 text-red-600" />;
      case "required": return <UploadIcon className="w-4 h-4 text-blue-600" />;
      default: return <FileTextIcon className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "approved": return "bg-green-100 text-green-800";
      case "pending": return "bg-yellow-100 text-yellow-800";
      case "rejected": return "bg-red-100 text-red-800";
      case "required": return "bg-blue-100 text-blue-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getKYCStatusColor = (status: string) => {
    switch (status) {
      case "approved": return "text-green-600";
      case "pending": return "text-yellow-600";
      case "required": return "text-blue-600";
      default: return "text-gray-600";
    }
  };

  const filteredDocuments = documents.filter(doc => {
    const matchesSearch = doc.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         doc.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesType = selectedType === "all" || doc.type === selectedType;
    return matchesSearch && matchesType;
  });

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-900">Documents & KYC</h1>
          <p className="text-slate-600 mt-1">Manage your documents and complete KYC verification</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <UploadIcon className="w-4 h-4 mr-2" />
          Upload Document
        </Button>
      </div>

      {/* KYC Status Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <IdCardIcon className="w-5 h-5" />
            KYC Verification Status
          </CardTitle>
          <CardDescription>Complete your KYC verification to unlock full platform access</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div className="text-center">
              <div className={`text-2xl font-bold ${getKYCStatusColor(kycStatus.overall)}`}>
                {kycStatus.overall === "complete" ? "✓" : kycStatus.overall === "pending" ? "⏳" : "⚠"}
              </div>
              <p className="text-sm font-medium">Overall Status</p>
              <p className={`text-xs ${getKYCStatusColor(kycStatus.overall)}`}>
                {kycStatus.overall.charAt(0).toUpperCase() + kycStatus.overall.slice(1)}
              </p>
            </div>
            <div className="text-center">
              <div className={`text-xl ${getKYCStatusColor(kycStatus.identity)}`}>
                {kycStatus.identity === "approved" ? "✓" : kycStatus.identity === "pending" ? "⏳" : "📋"}
              </div>
              <p className="text-sm font-medium">Identity</p>
              <p className={`text-xs ${getKYCStatusColor(kycStatus.identity)}`}>
                {kycStatus.identity.charAt(0).toUpperCase() + kycStatus.identity.slice(1)}
              </p>
            </div>
            <div className="text-center">
              <div className={`text-xl ${getKYCStatusColor(kycStatus.address)}`}>
                {kycStatus.address === "approved" ? "✓" : kycStatus.address === "pending" ? "⏳" : "📋"}
              </div>
              <p className="text-sm font-medium">Address</p>
              <p className={`text-xs ${getKYCStatusColor(kycStatus.address)}`}>
                {kycStatus.address.charAt(0).toUpperCase() + kycStatus.address.slice(1)}
              </p>
            </div>
            <div className="text-center">
              <div className={`text-xl ${getKYCStatusColor(kycStatus.income)}`}>
                {kycStatus.income === "approved" ? "✓" : kycStatus.income === "pending" ? "⏳" : "📋"}
              </div>
              <p className="text-sm font-medium">Income</p>
              <p className={`text-xs ${getKYCStatusColor(kycStatus.income)}`}>
                {kycStatus.income.charAt(0).toUpperCase() + kycStatus.income.slice(1)}
              </p>
            </div>
            <div className="text-center">
              <div className={`text-xl ${getKYCStatusColor(kycStatus.accreditation)}`}>
                {kycStatus.accreditation === "approved" ? "✓" : kycStatus.accreditation === "pending" ? "⏳" : "📋"}
              </div>
              <p className="text-sm font-medium">Accreditation</p>
              <p className={`text-xs ${getKYCStatusColor(kycStatus.accreditation)}`}>
                {kycStatus.accreditation.charAt(0).toUpperCase() + kycStatus.accreditation.slice(1)}
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Document Management */}
      <Tabs defaultValue="all" className="space-y-4">
        <div className="flex justify-between items-center">
          <TabsList>
            <TabsTrigger value="all">All Documents</TabsTrigger>
            <TabsTrigger value="kyc">KYC Documents</TabsTrigger>
            <TabsTrigger value="investments">Investment Docs</TabsTrigger>
            <TabsTrigger value="required">Required</TabsTrigger>
          </TabsList>
          
          <div className="flex gap-2">
            <div className="relative">
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Search documents..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10 w-64"
              />
            </div>
          </div>
        </div>

        <TabsContent value="all" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>All Documents</CardTitle>
              <CardDescription>Complete list of your uploaded documents</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {filteredDocuments.map((doc) => (
                  <div key={doc.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-slate-50">
                    <div className="flex items-center gap-4">
                      {getStatusIcon(doc.status)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium">{doc.name}</h3>
                          <Badge className={getStatusBadgeColor(doc.status)}>
                            {doc.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-slate-600">{doc.description}</p>
                        {doc.uploadDate && (
                          <p className="text-xs text-slate-500 mt-1">
                            Uploaded: {new Date(doc.uploadDate).toLocaleDateString()} • {doc.size}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {doc.status === "required" ? (
                        <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                          <UploadIcon className="w-4 h-4 mr-2" />
                          Upload
                        </Button>
                      ) : (
                        <>
                          <Button variant="outline" size="sm">
                            <EyeOpenIcon className="w-4 h-4 mr-2" />
                            View
                          </Button>
                          <Button variant="outline" size="sm">
                            <DownloadIcon className="w-4 h-4 mr-2" />
                            Download
                          </Button>
                        </>
                      )}
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <DotsVerticalIcon className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent>
                          <DropdownMenuItem>Replace Document</DropdownMenuItem>
                          <DropdownMenuItem>Share</DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="kyc" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>KYC Documents</CardTitle>
              <CardDescription>Documents required for Know Your Customer verification</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {filteredDocuments.filter(doc => doc.type === "kyc" || doc.type === "identity").map((doc) => (
                  <div key={doc.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-slate-50">
                    <div className="flex items-center gap-4">
                      {getStatusIcon(doc.status)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium">{doc.name}</h3>
                          <Badge className={getStatusBadgeColor(doc.status)}>
                            {doc.status}
                          </Badge>
                        </div>
                        <p className="text-sm text-slate-600">{doc.description}</p>
                        {doc.uploadDate && (
                          <p className="text-xs text-slate-500 mt-1">
                            Uploaded: {new Date(doc.uploadDate).toLocaleDateString()} • {doc.size}
                          </p>
                        )}
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      {doc.status === "required" ? (
                        <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                          <UploadIcon className="w-4 h-4 mr-2" />
                          Upload
                        </Button>
                      ) : (
                        <>
                          <Button variant="outline" size="sm">
                            <EyeOpenIcon className="w-4 h-4 mr-2" />
                            View
                          </Button>
                          <Button variant="outline" size="sm">
                            <DownloadIcon className="w-4 h-4 mr-2" />
                            Download
                          </Button>
                        </>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
