"use client";

import { DashboardPageWrapper } from "../DashboardLayout";
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

export function NotificationsAlerts() {
  return (
    <DashboardPageWrapper
      title="Notifications / Alerts"
      subtitle="Important notifications and system alerts"
    >
      <Card>
        <CardHeader>
          <CardTitle>Notifications & Alerts</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-500">Notifications & alerts view coming soon...</p>
        </CardContent>
      </Card>
    </DashboardPageWrapper>
  );
}
