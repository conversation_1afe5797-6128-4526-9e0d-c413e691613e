"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Progress } from "@/components/ui/progress";
import {
  BarChartIcon,
  TriangleUpIcon as TrendingUpIcon,
  TriangleDownIcon as TrendingDownIcon,
  CalendarIcon,
  DownloadIcon,
  EyeOpenIcon,
  PieChartIcon,
  ActivityLogIcon
} from "@radix-ui/react-icons";

interface PortfolioMetrics {
  totalInvested: number;
  currentValue: number;
  totalReturns: number;
  portfolioGrowth: number;
  activeInvestments: number;
  monthlyGrowth: number;
  yearlyGrowth: number;
  riskScore: number;
}

interface InvestmentPerformance {
  id: string;
  name: string;
  invested: number;
  currentValue: number;
  returns: number;
  growth: number;
  status: 'active' | 'completed' | 'pending';
  category: string;
}

interface MonthlyData {
  month: string;
  invested: number;
  returns: number;
  growth: number;
}

export function AnalyticsView() {
  const [metrics, setMetrics] = useState<PortfolioMetrics | null>(null);
  const [investments, setInvestments] = useState<InvestmentPerformance[]>([]);
  const [monthlyData, setMonthlyData] = useState<MonthlyData[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  const fetchAnalyticsData = async () => {
    try {
      setIsLoading(true);
      
      // Fetch portfolio metrics
      const metricsResponse = await fetch('/api/portfolio/stats');
      if (metricsResponse.ok) {
        const metricsData = await metricsResponse.json();
        setMetrics({
          ...metricsData,
          monthlyGrowth: 8.5,
          yearlyGrowth: 24.3,
          riskScore: 6.2
        });
      }

      // Fetch investment performance
      const investmentsResponse = await fetch('/api/investments');
      if (investmentsResponse.ok) {
        const investmentsData = await investmentsResponse.json();
        const performanceData = investmentsData.map((inv: any) => ({
          id: inv.id,
          name: inv.projectName || inv.title,
          invested: inv.amount || 0,
          currentValue: (inv.amount || 0) * 1.15, // Mock 15% growth
          returns: (inv.amount || 0) * 0.15,
          growth: 15.0,
          status: inv.status || 'active',
          category: inv.category || 'AI/Tech'
        }));
        setInvestments(performanceData);
      }

      // Mock monthly data
      setMonthlyData([
        { month: 'Jan', invested: 25000, returns: 2800, growth: 11.2 },
        { month: 'Feb', invested: 32000, returns: 4200, growth: 13.1 },
        { month: 'Mar', invested: 28000, returns: 3600, growth: 12.9 },
        { month: 'Apr', invested: 35000, returns: 5100, growth: 14.6 },
        { month: 'May', invested: 42000, returns: 6800, growth: 16.2 },
        { month: 'Jun', invested: 38000, returns: 5900, growth: 15.5 }
      ]);

    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const exportReport = () => {
    // Mock export functionality
    const reportData = {
      metrics,
      investments,
      monthlyData,
      generatedAt: new Date().toISOString()
    };
    
    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `portfolio-report-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-white">Portfolio Analytics</h1>
          <p className="text-slate-400">Comprehensive investment performance and insights</p>
        </div>
        <Button onClick={exportReport} className="bg-blue-600 hover:bg-blue-700">
          <DownloadIcon className="mr-2 h-4 w-4" />
          Export Report
        </Button>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-300">Total Portfolio Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              ${metrics?.currentValue?.toLocaleString() || '0'}
            </div>
            <div className="flex items-center text-sm text-emerald-400">
              <TrendingUpIcon className="mr-1 h-4 w-4" />
              +{metrics?.portfolioGrowth?.toFixed(1) || '0'}%
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-300">Total Returns</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              ${metrics?.totalReturns?.toLocaleString() || '0'}
            </div>
            <div className="flex items-center text-sm text-emerald-400">
              <TrendingUpIcon className="mr-1 h-4 w-4" />
              +{metrics?.monthlyGrowth || '0'}% this month
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-300">Active Investments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {metrics?.activeInvestments || investments.length}
            </div>
            <div className="flex items-center text-sm text-blue-400">
              <ActivityLogIcon className="mr-1 h-4 w-4" />
              Across {new Set(investments.map(i => i.category)).size} sectors
            </div>
          </CardContent>
        </Card>

        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-slate-300">Risk Score</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-white">
              {metrics?.riskScore || '6.2'}/10
            </div>
            <div className="flex items-center text-sm text-yellow-400">
              <BarChartIcon className="mr-1 h-4 w-4" />
              Moderate Risk
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Detailed Analytics */}
      <Tabs defaultValue="performance" className="space-y-4">
        <TabsList className="bg-slate-800 border-slate-700">
          <TabsTrigger value="performance" className="data-[state=active]:bg-blue-600">
            Performance
          </TabsTrigger>
          <TabsTrigger value="breakdown" className="data-[state=active]:bg-blue-600">
            Investment Breakdown
          </TabsTrigger>
          <TabsTrigger value="trends" className="data-[state=active]:bg-blue-600">
            Monthly Trends
          </TabsTrigger>
        </TabsList>

        <TabsContent value="performance" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Investment Performance</CardTitle>
              <CardDescription>Individual investment returns and growth</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {investments.map((investment) => (
                  <div key={investment.id} className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                    <div className="flex-1">
                      <div className="flex items-center gap-3">
                        <h3 className="font-medium text-white">{investment.name}</h3>
                        <Badge variant={investment.status === 'active' ? 'default' : 'secondary'}>
                          {investment.status}
                        </Badge>
                        <Badge variant="outline" className="text-xs">
                          {investment.category}
                        </Badge>
                      </div>
                      <div className="mt-2 flex items-center gap-6 text-sm text-slate-400">
                        <span>Invested: ${investment.invested.toLocaleString()}</span>
                        <span>Current: ${investment.currentValue.toLocaleString()}</span>
                        <span className="text-emerald-400">
                          Returns: +${investment.returns.toLocaleString()}
                        </span>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`text-lg font-semibold ${
                        investment.growth >= 0 ? 'text-emerald-400' : 'text-red-400'
                      }`}>
                        {investment.growth >= 0 ? '+' : ''}{investment.growth.toFixed(1)}%
                      </div>
                      <Progress 
                        value={Math.min(investment.growth, 100)} 
                        className="w-20 mt-1"
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="breakdown" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Investment Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {Array.from(new Set(investments.map(i => i.category))).map((category) => {
                    const categoryInvestments = investments.filter(i => i.category === category);
                    const totalInvested = categoryInvestments.reduce((sum, i) => sum + i.invested, 0);
                    const totalValue = categoryInvestments.reduce((sum, i) => sum + i.currentValue, 0);
                    const percentage = metrics?.totalInvested ? (totalInvested / metrics.totalInvested) * 100 : 0;
                    
                    return (
                      <div key={category} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-white font-medium">{category}</span>
                          <span className="text-slate-400">{percentage.toFixed(1)}%</span>
                        </div>
                        <Progress value={percentage} className="h-2" />
                        <div className="flex justify-between text-sm text-slate-400">
                          <span>${totalInvested.toLocaleString()}</span>
                          <span className="text-emerald-400">
                            +${(totalValue - totalInvested).toLocaleString()}
                          </span>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>

            <Card className="bg-slate-800/50 border-slate-700">
              <CardHeader>
                <CardTitle className="text-white">Risk Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-white">Low Risk</span>
                      <span className="text-slate-400">25%</span>
                    </div>
                    <Progress value={25} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-white">Medium Risk</span>
                      <span className="text-slate-400">60%</span>
                    </div>
                    <Progress value={60} className="h-2" />
                  </div>
                  <div className="space-y-2">
                    <div className="flex justify-between items-center">
                      <span className="text-white">High Risk</span>
                      <span className="text-slate-400">15%</span>
                    </div>
                    <Progress value={15} className="h-2" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="trends" className="space-y-4">
          <Card className="bg-slate-800/50 border-slate-700">
            <CardHeader>
              <CardTitle className="text-white">Monthly Performance Trends</CardTitle>
              <CardDescription>Investment activity and returns over time</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {monthlyData.map((month) => (
                  <div key={month.month} className="flex items-center justify-between p-4 bg-slate-700/30 rounded-lg">
                    <div className="flex items-center gap-4">
                      <div className="w-12 h-12 bg-blue-600/20 rounded-lg flex items-center justify-center">
                        <CalendarIcon className="h-6 w-6 text-blue-400" />
                      </div>
                      <div>
                        <h3 className="font-medium text-white">{month.month} 2024</h3>
                        <p className="text-sm text-slate-400">
                          Invested: ${month.invested.toLocaleString()}
                        </p>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-semibold text-emerald-400">
                        +${month.returns.toLocaleString()}
                      </div>
                      <div className="text-sm text-slate-400">
                        {month.growth.toFixed(1)}% growth
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
