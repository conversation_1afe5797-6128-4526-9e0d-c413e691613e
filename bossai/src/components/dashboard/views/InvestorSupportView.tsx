"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import {
  QuestionMarkCircledIcon,
  ChatBubbleIcon,
  EnvelopeClosedIcon,
  MobileIcon,
  ClockIcon,
  CheckCircledIcon,
  ExclamationTriangleIcon,
  MagnifyingGlassIcon,
  PlusIcon,
  FileTextIcon
} from "@radix-ui/react-icons";

interface SupportTicket {
  id: string;
  subject: string;
  status: "open" | "in_progress" | "resolved" | "closed";
  priority: "low" | "medium" | "high" | "urgent";
  category: "technical" | "account" | "investment" | "billing" | "general";
  createdDate: string;
  lastUpdate: string;
  description: string;
}

interface FAQItem {
  id: string;
  question: string;
  answer: string;
  category: string;
  helpful: number;
}

export function InvestorSupportView() {
  const [searchTerm, setSearchTerm] = useState("");
  const [newTicketSubject, setNewTicketSubject] = useState("");
  const [newTicketDescription, setNewTicketDescription] = useState("");

  // Mock support tickets
  const supportTickets: SupportTicket[] = [
    {
      id: "1",
      subject: "Unable to access investment documents",
      status: "in_progress",
      priority: "medium",
      category: "technical",
      createdDate: "2024-01-20",
      lastUpdate: "2024-01-21",
      description: "I'm having trouble downloading my investment agreement for the AI Healthcare project."
    },
    {
      id: "2",
      subject: "Question about portfolio performance",
      status: "resolved",
      priority: "low",
      category: "investment",
      createdDate: "2024-01-15",
      lastUpdate: "2024-01-16",
      description: "Can you explain how the portfolio returns are calculated?"
    },
    {
      id: "3",
      subject: "KYC verification status",
      status: "closed",
      priority: "high",
      category: "account",
      createdDate: "2024-01-10",
      lastUpdate: "2024-01-12",
      description: "My KYC verification has been pending for over a week."
    }
  ];

  // Mock FAQ data
  const faqItems: FAQItem[] = [
    {
      id: "1",
      question: "How do I complete my KYC verification?",
      answer: "To complete KYC verification, upload your government-issued ID, proof of address, and income verification documents in the Documents section.",
      category: "Account",
      helpful: 45
    },
    {
      id: "2",
      question: "What is the minimum investment amount?",
      answer: "The minimum investment varies by project, typically ranging from $10,000 to $50,000 for individual investors.",
      category: "Investment",
      helpful: 38
    },
    {
      id: "3",
      question: "How are investment returns calculated?",
      answer: "Returns are calculated based on the project's performance and your investment amount. You'll receive detailed reports quarterly.",
      category: "Investment",
      helpful: 42
    },
    {
      id: "4",
      question: "Can I withdraw my investment early?",
      answer: "Early withdrawal terms depend on the specific investment agreement. Most investments have a minimum holding period.",
      category: "Investment",
      helpful: 29
    },
    {
      id: "5",
      question: "How do I update my contact information?",
      answer: "You can update your contact information in the Account Settings section under the Profile tab.",
      category: "Account",
      helpful: 33
    },
    {
      id: "6",
      question: "What happens if a project fails?",
      answer: "Investment risks are disclosed in each project's documentation. We conduct thorough due diligence, but all investments carry risk.",
      category: "Investment",
      helpful: 51
    }
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "open": return <ExclamationTriangleIcon className="w-4 h-4 text-red-600" />;
      case "in_progress": return <ClockIcon className="w-4 h-4 text-yellow-600" />;
      case "resolved": return <CheckCircledIcon className="w-4 h-4 text-green-600" />;
      case "closed": return <CheckCircledIcon className="w-4 h-4 text-gray-600" />;
      default: return <QuestionMarkCircledIcon className="w-4 h-4 text-gray-600" />;
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case "open": return "bg-red-100 text-red-800";
      case "in_progress": return "bg-yellow-100 text-yellow-800";
      case "resolved": return "bg-green-100 text-green-800";
      case "closed": return "bg-gray-100 text-gray-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityBadgeColor = (priority: string) => {
    switch (priority) {
      case "urgent": return "bg-red-100 text-red-800";
      case "high": return "bg-orange-100 text-orange-800";
      case "medium": return "bg-yellow-100 text-yellow-800";
      case "low": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const filteredFAQs = faqItems.filter(faq =>
    faq.question.toLowerCase().includes(searchTerm.toLowerCase()) ||
    faq.answer.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleCreateTicket = () => {
    if (!newTicketSubject.trim() || !newTicketDescription.trim()) {
      alert("Please fill in all fields");
      return;
    }
    // Handle ticket creation logic
    console.log("Creating ticket:", { subject: newTicketSubject, description: newTicketDescription });
    setNewTicketSubject("");
    setNewTicketDescription("");
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-900">Support & Help</h1>
          <p className="text-slate-600 mt-1">Get help with your account and investments</p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <PlusIcon className="w-4 h-4 mr-2" />
          New Ticket
        </Button>
      </div>

      {/* Quick Contact Options */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <ChatBubbleIcon className="w-8 h-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Live Chat</h3>
            <p className="text-sm text-slate-600 mb-3">Get instant help from our support team</p>
            <Badge className="bg-green-100 text-green-800">Available Now</Badge>
          </CardContent>
        </Card>
        
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <EnvelopeClosedIcon className="w-8 h-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Email Support</h3>
            <p className="text-sm text-slate-600 mb-3">Send us a detailed message</p>
            <p className="text-xs text-slate-500"><EMAIL></p>
          </CardContent>
        </Card>
        
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <MobileIcon className="w-8 h-8 text-blue-600 mx-auto mb-3" />
            <h3 className="font-semibold mb-2">Phone Support</h3>
            <p className="text-sm text-slate-600 mb-3">Speak with our team directly</p>
            <p className="text-xs text-slate-500">+****************</p>
          </CardContent>
        </Card>
      </div>

      {/* Support Tabs */}
      <Tabs defaultValue="tickets" className="space-y-4">
        <TabsList>
          <TabsTrigger value="tickets">My Tickets</TabsTrigger>
          <TabsTrigger value="faq">FAQ</TabsTrigger>
          <TabsTrigger value="create">Create Ticket</TabsTrigger>
          <TabsTrigger value="resources">Resources</TabsTrigger>
        </TabsList>

        <TabsContent value="tickets" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Support Tickets</CardTitle>
              <CardDescription>Track your support requests and their status</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {supportTickets.map((ticket) => (
                  <div key={ticket.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-slate-50">
                    <div className="flex items-center gap-4">
                      {getStatusIcon(ticket.status)}
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <h3 className="font-medium">{ticket.subject}</h3>
                          <Badge className={getStatusBadgeColor(ticket.status)}>
                            {ticket.status.replace('_', ' ')}
                          </Badge>
                          <Badge className={getPriorityBadgeColor(ticket.priority)}>
                            {ticket.priority}
                          </Badge>
                        </div>
                        <p className="text-sm text-slate-600 mb-1">{ticket.description}</p>
                        <p className="text-xs text-slate-500">
                          Created: {new Date(ticket.createdDate).toLocaleDateString()} • 
                          Last update: {new Date(ticket.lastUpdate).toLocaleDateString()}
                        </p>
                      </div>
                    </div>
                    <Button variant="outline" size="sm">
                      View Details
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="faq" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Frequently Asked Questions</CardTitle>
              <CardDescription>Find quick answers to common questions</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="mb-4">
                <div className="relative">
                  <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                  <Input
                    placeholder="Search FAQ..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="space-y-4">
                {filteredFAQs.map((faq) => (
                  <div key={faq.id} className="border rounded-lg p-4">
                    <div className="flex items-start justify-between mb-2">
                      <h3 className="font-medium text-slate-900">{faq.question}</h3>
                      <Badge variant="outline" className="ml-2">
                        {faq.category}
                      </Badge>
                    </div>
                    <p className="text-slate-600 mb-3">{faq.answer}</p>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-slate-500">{faq.helpful} people found this helpful</span>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          👍 Helpful
                        </Button>
                        <Button variant="outline" size="sm">
                          👎 Not helpful
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="create" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Create Support Ticket</CardTitle>
              <CardDescription>Submit a new support request</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div>
                <label className="text-sm font-medium">Subject</label>
                <Input
                  placeholder="Brief description of your issue"
                  value={newTicketSubject}
                  onChange={(e) => setNewTicketSubject(e.target.value)}
                />
              </div>
              <div>
                <label className="text-sm font-medium">Category</label>
                <select className="w-full p-2 border rounded-md">
                  <option value="general">General</option>
                  <option value="technical">Technical</option>
                  <option value="account">Account</option>
                  <option value="investment">Investment</option>
                  <option value="billing">Billing</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium">Priority</label>
                <select className="w-full p-2 border rounded-md">
                  <option value="low">Low</option>
                  <option value="medium">Medium</option>
                  <option value="high">High</option>
                  <option value="urgent">Urgent</option>
                </select>
              </div>
              <div>
                <label className="text-sm font-medium">Description</label>
                <textarea
                  className="w-full p-2 border rounded-md h-32"
                  placeholder="Please provide detailed information about your issue..."
                  value={newTicketDescription}
                  onChange={(e) => setNewTicketDescription(e.target.value)}
                />
              </div>
              <div className="flex justify-end">
                <Button onClick={handleCreateTicket} className="bg-blue-600 hover:bg-blue-700">
                  Submit Ticket
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="resources" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Help Resources</CardTitle>
              <CardDescription>Additional resources and documentation</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg">
                  <FileTextIcon className="w-8 h-8 text-blue-600 mb-3" />
                  <h3 className="font-semibold mb-2">Investment Guide</h3>
                  <p className="text-sm text-slate-600 mb-3">
                    Comprehensive guide to understanding our investment platform
                  </p>
                  <Button variant="outline" size="sm">
                    Download PDF
                  </Button>
                </div>
                <div className="p-4 border rounded-lg">
                  <FileTextIcon className="w-8 h-8 text-blue-600 mb-3" />
                  <h3 className="font-semibold mb-2">KYC Requirements</h3>
                  <p className="text-sm text-slate-600 mb-3">
                    Step-by-step guide for completing your KYC verification
                  </p>
                  <Button variant="outline" size="sm">
                    View Guide
                  </Button>
                </div>
                <div className="p-4 border rounded-lg">
                  <FileTextIcon className="w-8 h-8 text-blue-600 mb-3" />
                  <h3 className="font-semibold mb-2">Risk Disclosure</h3>
                  <p className="text-sm text-slate-600 mb-3">
                    Important information about investment risks
                  </p>
                  <Button variant="outline" size="sm">
                    Read Document
                  </Button>
                </div>
                <div className="p-4 border rounded-lg">
                  <FileTextIcon className="w-8 h-8 text-blue-600 mb-3" />
                  <h3 className="font-semibold mb-2">Platform Tutorial</h3>
                  <p className="text-sm text-slate-600 mb-3">
                    Video walkthrough of platform features
                  </p>
                  <Button variant="outline" size="sm">
                    Watch Video
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
