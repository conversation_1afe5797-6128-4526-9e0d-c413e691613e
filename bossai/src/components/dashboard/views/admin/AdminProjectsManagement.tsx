"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  PlusIcon,
  SearchIcon,
  EditIcon,
  TrashIcon,
  EyeIcon,
  DollarSignIcon,
  UsersIcon,
  CalendarIcon,
  TrendingUpIcon
} from "lucide-react";

interface Project {
  id: string;
  title: string;
  description: string;
  sector: string;
  fundingGoal: number;
  currentFunding: number;
  roiEstimate: number;
  status: "DRAFT" | "ACTIVE" | "FUNDING" | "FUNDED" | "COMPLETED" | "CANCELLED";
  investorCount: number;
  startDate: string;
  endDate: string;
  riskLevel: "CONSERVATIVE" | "MODERATE" | "AGGRESSIVE" | "VERY_AGGRESSIVE";
  featured: boolean;
}

export function AdminProjectsManagement() {
  const [projects, setProjects] = useState<Project[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("ALL");
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch real projects from API
    const fetchProjects = async () => {
      try {
        setLoading(true);
        const response = await fetch('/api/admin/projects');
        if (response.ok) {
          const data = await response.json();
          setProjects(data.projects || []);
        } else {
          console.error('Failed to fetch projects');
        }
      } catch (error) {
        console.error('Error fetching projects:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchProjects();
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getStatusBadge = (status: Project['status']) => {
    const statusConfig = {
      DRAFT: { color: "bg-gray-500", text: "Draft" },
      ACTIVE: { color: "bg-blue-500", text: "Active" },
      FUNDING: { color: "bg-yellow-500", text: "Funding" },
      FUNDED: { color: "bg-green-500", text: "Funded" },
      COMPLETED: { color: "bg-purple-500", text: "Completed" },
      CANCELLED: { color: "bg-red-500", text: "Cancelled" }
    };

    const config = statusConfig[status];
    return (
      <Badge className={`${config.color} text-white`}>
        {config.text}
      </Badge>
    );
  };

  const getRiskBadge = (riskLevel: Project['riskLevel']) => {
    const riskConfig = {
      CONSERVATIVE: { color: "bg-green-600", text: "Conservative" },
      MODERATE: { color: "bg-yellow-600", text: "Moderate" },
      AGGRESSIVE: { color: "bg-orange-600", text: "Aggressive" },
      VERY_AGGRESSIVE: { color: "bg-red-600", text: "Very Aggressive" }
    };

    const config = riskConfig[riskLevel];
    return (
      <Badge className={`${config.color} text-white`}>
        {config.text}
      </Badge>
    );
  };

  const filteredProjects = projects.filter(project => {
    const matchesSearch = project.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         project.sector.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === "ALL" || project.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const getFundingProgress = (current: number, goal: number) => {
    return Math.min((current / goal) * 100, 100);
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className="space-y-6"
    >
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-white mb-2">Projects Management</h1>
          <p className="text-slate-400">Create, edit, and manage all investment projects</p>
        </div>
        <Button className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
          <PlusIcon className="h-4 w-4 mr-2" />
          Create New Project
        </Button>
      </div>

      {/* Filters */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <SearchIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 text-slate-400 h-4 w-4" />
                <Input
                  placeholder="Search projects by title or sector..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 bg-slate-700 border-slate-600 text-white placeholder-slate-400"
                />
              </div>
            </div>
            <select
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="px-4 py-2 bg-slate-700 border border-slate-600 rounded-md text-white"
            >
              <option value="ALL">All Status</option>
              <option value="DRAFT">Draft</option>
              <option value="ACTIVE">Active</option>
              <option value="FUNDING">Funding</option>
              <option value="FUNDED">Funded</option>
              <option value="COMPLETED">Completed</option>
              <option value="CANCELLED">Cancelled</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Projects Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {filteredProjects.map((project) => (
          <Card key={project.id} className="bg-slate-800/50 border-slate-700 hover:bg-slate-700/50 transition-colors">
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <CardTitle className="text-white text-lg">{project.title}</CardTitle>
                    {project.featured && (
                      <Badge className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white">
                        Featured
                      </Badge>
                    )}
                  </div>
                  <p className="text-slate-400 text-sm mb-3">{project.description}</p>
                  <div className="flex items-center gap-4 text-sm">
                    {getStatusBadge(project.status)}
                    {getRiskBadge(project.riskLevel)}
                    <Badge variant="outline" className="text-slate-300 border-slate-600">
                      {project.sector}
                    </Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              {/* Funding Progress */}
              <div className="mb-4">
                <div className="flex justify-between text-sm mb-2">
                  <span className="text-slate-400">Funding Progress</span>
                  <span className="text-white">
                    {formatCurrency(project.currentFunding)} / {formatCurrency(project.fundingGoal)}
                  </span>
                </div>
                <div className="w-full bg-slate-700 rounded-full h-2">
                  <div
                    className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${getFundingProgress(project.currentFunding, project.fundingGoal)}%` }}
                  />
                </div>
                <p className="text-xs text-slate-400 mt-1">
                  {getFundingProgress(project.currentFunding, project.fundingGoal).toFixed(1)}% funded
                </p>
              </div>

              {/* Project Stats */}
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <TrendingUpIcon className="h-4 w-4 text-green-400 mr-1" />
                    <span className="text-white text-sm font-semibold">{project.roiEstimate}%</span>
                  </div>
                  <p className="text-xs text-slate-400">ROI Est.</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <UsersIcon className="h-4 w-4 text-blue-400 mr-1" />
                    <span className="text-white text-sm font-semibold">{project.investorCount}</span>
                  </div>
                  <p className="text-xs text-slate-400">Investors</p>
                </div>
                <div className="text-center">
                  <div className="flex items-center justify-center mb-1">
                    <CalendarIcon className="h-4 w-4 text-purple-400 mr-1" />
                    <span className="text-white text-sm font-semibold">
                      {new Date(project.endDate).getFullYear()}
                    </span>
                  </div>
                  <p className="text-xs text-slate-400">End Year</p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-2">
                <Button size="sm" variant="outline" className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700">
                  <EyeIcon className="h-4 w-4 mr-1" />
                  View
                </Button>
                <Button size="sm" variant="outline" className="flex-1 border-slate-600 text-slate-300 hover:bg-slate-700">
                  <EditIcon className="h-4 w-4 mr-1" />
                  Edit
                </Button>
                <Button size="sm" variant="outline" className="border-red-600 text-red-400 hover:bg-red-600/10">
                  <TrashIcon className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {filteredProjects.length === 0 && (
        <Card className="bg-slate-800/50 border-slate-700">
          <CardContent className="p-12 text-center">
            <p className="text-slate-400 text-lg">No projects found matching your criteria.</p>
            <Button className="mt-4 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
              <PlusIcon className="h-4 w-4 mr-2" />
              Create Your First Project
            </Button>
          </CardContent>
        </Card>
      )}
    </motion.div>
  );
}
