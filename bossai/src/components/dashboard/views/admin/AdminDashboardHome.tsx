"use client";

import { useState, useEffect } from "react";
import { motion } from "framer-motion";
import { <PERSON>Card, AICardContent, AICardHeader, AICardTitle, AIMetricCard } from "@/components/ui/ai-card";
import { AIButton } from "@/components/ui/ai-button";
import { Badge } from "@/components/ui/badge";

import {
  PersonIcon as UsersIcon,
  CircleIcon as DollarSignIcon,
  TriangleUpIcon as TrendingUpIcon,
  ClockIcon,
  ExclamationTriangleIcon as AlertTriangleIcon,
  CheckCircledIcon as CheckCircleIcon,
  CrossCircledIcon as XCircleIcon,
  PlusIcon
} from "@radix-ui/react-icons";

interface PlatformStats {
  totalInvestors: number;
  totalCapital: number;
  activeProjects: number;
  pendingApprovals: number;
  monthlyGrowth: number;
  completedInvestments: number;
}

interface RecentActivity {
  id: string;
  type: "project_created" | "investment_submitted" | "document_uploaded" | "investor_approved";
  description: string;
  timestamp: string;
  status: "pending" | "approved" | "rejected";
}

export function AdminDashboardHome() {
  const [platformStats, setPlatformStats] = useState<PlatformStats | null>(null);
  const [recentActivity, setRecentActivity] = useState<RecentActivity[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const load = async () => {
      try {
        const res = await fetch('/api/dashboard/metrics');
        if (!res.ok) throw new Error('Failed to fetch metrics');
        const m = await res.json();
        setPlatformStats({
          totalInvestors: m.totalInvestors ?? 0,
          totalCapital: m.totalFunding ?? 0,
          activeProjects: m.activeProjects ?? 0,
          pendingApprovals: m.pendingKyc ?? 0,
          monthlyGrowth: m.monthlyGrowth ?? 0,
          completedInvestments: 0,
        });
        setRecentActivity([]);
      } catch (e) {
        console.error(e);
      } finally {
        setLoading(false);
      }
    };
    load();

    setTimeout(() => {
      setPlatformStats(mockStats);
      setRecentActivity(mockActivity);
      setLoading(false);
    }, 1000);
  }, []);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getActivityIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case "project_created":
        return <PlusIcon className="h-4 w-4 text-green-400" />;
      case "investment_submitted":
        return <DollarSignIcon className="h-4 w-4 text-blue-400" />;
      case "document_uploaded":
        return <ClockIcon className="h-4 w-4 text-yellow-400" />;
      case "investor_approved":
        return <CheckCircleIcon className="h-4 w-4 text-green-400" />;
      default:
        return <AlertTriangleIcon className="h-4 w-4 text-gray-400" />;
    }
  };

  const getStatusBadge = (status: RecentActivity['status']) => {
    switch (status) {
      case "pending":
        return <Badge variant="outline" className="text-yellow-400 border-yellow-400">Pending</Badge>;
      case "approved":
        return <Badge variant="outline" className="text-green-400 border-green-400">Approved</Badge>;
      case "rejected":
        return <Badge variant="outline" className="text-red-400 border-red-400">Rejected</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400"></div>
      </div>
    );
  }

  return (
    <div className="px-4 lg:pl-0 lg:pr-6 py-6 space-y-8">
      {/* Page Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-4xl font-bold text-white glow-text">Admin Dashboard</h1>
          <p className="text-xl text-blue-200 mt-2">Platform overview and management center</p>
        </div>
        <AIButton variant="primary" className="gap-2">
          <PlusIcon className="h-5 w-5" />
          Create Project
        </AIButton>
      </div>
      {/* Platform Stats with AI Metric Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <AIMetricCard
          title="Total Investors"
          value={`${platformStats?.totalInvestors ?? 0}`}
          change={`${(platformStats?.monthlyGrowth ?? 0) >= 0 ? '+' : ''}${(platformStats?.monthlyGrowth ?? 0).toFixed(1)}% this month`}
          trend={(platformStats?.monthlyGrowth ?? 0) >= 0 ? 'up' : 'down'}
          icon={<UsersIcon className="h-8 w-8" />}
        />

        <AIMetricCard
          title="Total Capital"
          value={`$${(platformStats?.totalCapital ?? 0).toLocaleString()}`}
          change={`${(platformStats?.monthlyGrowth ?? 0) >= 0 ? '+' : ''}${(platformStats?.monthlyGrowth ?? 0).toFixed(1)}% this month`}
          trend={(platformStats?.monthlyGrowth ?? 0) >= 0 ? 'up' : 'down'}
          icon={<DollarSignIcon className="h-8 w-8" />}
        />

        <AIMetricCard
          title="Active Projects"
          value={`${platformStats?.activeProjects ?? 0}`}
          change={`${platformStats?.activeProjects ?? 0} active`}
          trend="neutral"
          icon={<TrendingUpIcon className="h-8 w-8" />}
        />

        <AIMetricCard
          title="Pending Approvals"
          value={`${platformStats?.pendingApprovals ?? 0}`}
          change={`${platformStats?.pendingApprovals ?? 0} pending`}
          trend="neutral"
          icon={<AlertTriangleIcon className="h-8 w-8" />}
        />
      </div>

      {/* Recent Activity with AI Card */}
      <AICard variant="glass" className="p-0">
        <AICardHeader>
          <AICardTitle className="text-white glow-text">Recent Activity</AICardTitle>
        </AICardHeader>
        <AICardContent>
          <div className="space-y-4">
            {recentActivity.map((activity) => (
              <motion.div
                key={activity.id}
                className="flex items-center justify-between p-4 bg-blue-500/10 rounded-xl border border-blue-500/20 backdrop-blur-sm"
                whileHover={{ scale: 1.02, x: 4 }}
                transition={{ duration: 0.2 }}
              >
                <div className="flex items-center space-x-4">
                  {getActivityIcon(activity.type)}
                  <div>
                    <p className="text-white text-sm font-medium">{activity.description}</p>
                    <p className="text-blue-200 text-xs">{activity.timestamp}</p>
                  </div>
                </div>
                {getStatusBadge(activity.status)}
              </motion.div>
            ))}
          </div>
        </AICardContent>
      </AICard>

      {/* Quick Actions with AI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <AICard variant="gradient" hover className="cursor-pointer">
          <AICardContent className="p-8 text-center">
            <UsersIcon className="h-16 w-16 text-blue-400 mx-auto mb-6" />
            <h3 className="text-white font-bold text-lg mb-3 glow-text">Manage Investors</h3>
            <p className="text-blue-200 text-sm">Review and approve investor accounts</p>
          </AICardContent>
        </AICard>

        <AICard variant="gradient" hover className="cursor-pointer">
          <AICardContent className="p-8 text-center">
            <ClockIcon className="h-16 w-16 text-yellow-400 mx-auto mb-6" />
            <h3 className="text-white font-bold text-lg mb-3 glow-text">Review Submissions</h3>
            <p className="text-blue-200 text-sm">Process pending investment forms</p>
          </AICardContent>
        </AICard>

        <AICard variant="gradient" hover className="cursor-pointer">
          <AICardContent className="p-8 text-center">
            <TrendingUpIcon className="h-16 w-16 text-green-400 mx-auto mb-6" />
            <h3 className="text-white font-bold text-lg mb-3 glow-text">View Analytics</h3>
            <p className="text-blue-200 text-sm">Platform performance insights</p>
          </AICardContent>
        </AICard>
      </div>
    </div>
  );
}
