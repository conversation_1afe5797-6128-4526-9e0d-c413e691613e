"use client";

import React, { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  PersonIcon,
  GearIcon,
  BellIcon,
  LockClosedIcon,
  IdCardIcon as CreditCardIcon,
  LockClosedIcon as ShieldIcon,
  EyeOpenIcon,
  EyeNoneIcon,
  CheckIcon,
  Cross2Icon
} from "@radix-ui/react-icons";

interface UserProfile {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
  investorType: "individual" | "institutional";
  accreditedStatus: "verified" | "pending" | "not_verified";
}

interface NotificationSettings {
  emailNotifications: boolean;
  smsNotifications: boolean;
  investmentUpdates: boolean;
  marketingEmails: boolean;
  securityAlerts: boolean;
  monthlyReports: boolean;
}

interface SecuritySettings {
  twoFactorEnabled: boolean;
  lastPasswordChange: string;
  loginSessions: number;
}

export function InvestorAccountView() {
  const [showPassword, setShowPassword] = useState(false);
  const [currentPassword, setCurrentPassword] = useState("");
  const [newPassword, setNewPassword] = useState("");
  const [confirmPassword, setConfirmPassword] = useState("");

  // Mock user profile data
  const [userProfile, setUserProfile] = useState<UserProfile>({
    firstName: "John",
    lastName: "Investor",
    email: "<EMAIL>",
    phone: "+****************",
    address: "123 Investment Street",
    city: "New York",
    state: "NY",
    zipCode: "10001",
    country: "United States",
    investorType: "individual",
    accreditedStatus: "verified"
  });

  const [notifications, setNotifications] = useState<NotificationSettings>({
    emailNotifications: true,
    smsNotifications: false,
    investmentUpdates: true,
    marketingEmails: false,
    securityAlerts: true,
    monthlyReports: true
  });

  const securityInfo: SecuritySettings = {
    twoFactorEnabled: true,
    lastPasswordChange: "2024-01-15",
    loginSessions: 3
  };

  const handleProfileUpdate = () => {
    // Handle profile update logic
    console.log("Profile updated:", userProfile);
  };

  const handleNotificationUpdate = (key: keyof NotificationSettings) => {
    setNotifications(prev => ({
      ...prev,
      [key]: !prev[key]
    }));
  };

  const handlePasswordChange = () => {
    if (newPassword !== confirmPassword) {
      alert("Passwords don't match");
      return;
    }
    // Handle password change logic
    console.log("Password changed");
    setCurrentPassword("");
    setNewPassword("");
    setConfirmPassword("");
  };

  const getAccreditationBadge = (status: string) => {
    switch (status) {
      case "verified":
        return <Badge className="bg-green-100 text-green-800">Verified Accredited</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Verification Pending</Badge>;
      case "not_verified":
        return <Badge className="bg-red-100 text-red-800">Not Verified</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">Unknown</Badge>;
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold text-slate-900">Account Settings</h1>
          <p className="text-slate-600 mt-1">Manage your account information and preferences</p>
        </div>
      </div>

      {/* Account Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PersonIcon className="w-5 h-5" />
            Account Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <p className="text-sm text-slate-600">Account Type</p>
              <p className="font-medium capitalize">{userProfile.investorType} Investor</p>
            </div>
            <div>
              <p className="text-sm text-slate-600">Accreditation Status</p>
              <div className="mt-1">
                {getAccreditationBadge(userProfile.accreditedStatus)}
              </div>
            </div>
            <div>
              <p className="text-sm text-slate-600">Member Since</p>
              <p className="font-medium">January 2024</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Settings Tabs */}
      <Tabs defaultValue="profile" className="space-y-4">
        <TabsList>
          <TabsTrigger value="profile">Profile</TabsTrigger>
          <TabsTrigger value="notifications">Notifications</TabsTrigger>
          <TabsTrigger value="security">Security</TabsTrigger>
          <TabsTrigger value="billing">Billing</TabsTrigger>
        </TabsList>

        <TabsContent value="profile" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Personal Information</CardTitle>
              <CardDescription>Update your personal details and contact information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium">First Name</label>
                  <Input
                    value={userProfile.firstName}
                    onChange={(e) => setUserProfile(prev => ({ ...prev, firstName: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Last Name</label>
                  <Input
                    value={userProfile.lastName}
                    onChange={(e) => setUserProfile(prev => ({ ...prev, lastName: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Email</label>
                  <Input
                    type="email"
                    value={userProfile.email}
                    onChange={(e) => setUserProfile(prev => ({ ...prev, email: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Phone</label>
                  <Input
                    value={userProfile.phone}
                    onChange={(e) => setUserProfile(prev => ({ ...prev, phone: e.target.value }))}
                  />
                </div>
                <div className="md:col-span-2">
                  <label className="text-sm font-medium">Address</label>
                  <Input
                    value={userProfile.address}
                    onChange={(e) => setUserProfile(prev => ({ ...prev, address: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">City</label>
                  <Input
                    value={userProfile.city}
                    onChange={(e) => setUserProfile(prev => ({ ...prev, city: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">State</label>
                  <Input
                    value={userProfile.state}
                    onChange={(e) => setUserProfile(prev => ({ ...prev, state: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">ZIP Code</label>
                  <Input
                    value={userProfile.zipCode}
                    onChange={(e) => setUserProfile(prev => ({ ...prev, zipCode: e.target.value }))}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium">Country</label>
                  <Input
                    value={userProfile.country}
                    onChange={(e) => setUserProfile(prev => ({ ...prev, country: e.target.value }))}
                  />
                </div>
              </div>
              <div className="flex justify-end">
                <Button onClick={handleProfileUpdate} className="bg-blue-600 hover:bg-blue-700">
                  Save Changes
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BellIcon className="w-5 h-5" />
                Notification Preferences
              </CardTitle>
              <CardDescription>Choose how you want to receive updates and alerts</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Email Notifications</p>
                    <p className="text-sm text-slate-600">Receive general notifications via email</p>
                  </div>
                  <Button
                    variant={notifications.emailNotifications ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleNotificationUpdate("emailNotifications")}
                  >
                    {notifications.emailNotifications ? <CheckIcon className="w-4 h-4" /> : <Cross2Icon className="w-4 h-4" />}
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">SMS Notifications</p>
                    <p className="text-sm text-slate-600">Receive urgent alerts via SMS</p>
                  </div>
                  <Button
                    variant={notifications.smsNotifications ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleNotificationUpdate("smsNotifications")}
                  >
                    {notifications.smsNotifications ? <CheckIcon className="w-4 h-4" /> : <Cross2Icon className="w-4 h-4" />}
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Investment Updates</p>
                    <p className="text-sm text-slate-600">Get notified about your investment performance</p>
                  </div>
                  <Button
                    variant={notifications.investmentUpdates ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleNotificationUpdate("investmentUpdates")}
                  >
                    {notifications.investmentUpdates ? <CheckIcon className="w-4 h-4" /> : <Cross2Icon className="w-4 h-4" />}
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Marketing Emails</p>
                    <p className="text-sm text-slate-600">Receive promotional content and offers</p>
                  </div>
                  <Button
                    variant={notifications.marketingEmails ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleNotificationUpdate("marketingEmails")}
                  >
                    {notifications.marketingEmails ? <CheckIcon className="w-4 h-4" /> : <Cross2Icon className="w-4 h-4" />}
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Security Alerts</p>
                    <p className="text-sm text-slate-600">Important security notifications</p>
                  </div>
                  <Button
                    variant={notifications.securityAlerts ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleNotificationUpdate("securityAlerts")}
                  >
                    {notifications.securityAlerts ? <CheckIcon className="w-4 h-4" /> : <Cross2Icon className="w-4 h-4" />}
                  </Button>
                </div>
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">Monthly Reports</p>
                    <p className="text-sm text-slate-600">Receive monthly portfolio summaries</p>
                  </div>
                  <Button
                    variant={notifications.monthlyReports ? "default" : "outline"}
                    size="sm"
                    onClick={() => handleNotificationUpdate("monthlyReports")}
                  >
                    {notifications.monthlyReports ? <CheckIcon className="w-4 h-4" /> : <Cross2Icon className="w-4 h-4" />}
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="security" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <ShieldIcon className="w-5 h-5" />
                Security Settings
              </CardTitle>
              <CardDescription>Manage your account security and authentication</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Two-Factor Authentication */}
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium">Two-Factor Authentication</p>
                  <p className="text-sm text-slate-600">
                    {securityInfo.twoFactorEnabled ? "Enabled" : "Disabled"} • Add an extra layer of security
                  </p>
                </div>
                <Badge className={securityInfo.twoFactorEnabled ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                  {securityInfo.twoFactorEnabled ? "Enabled" : "Disabled"}
                </Badge>
              </div>

              {/* Password Change */}
              <div className="space-y-4">
                <h4 className="font-medium">Change Password</h4>
                <div className="space-y-3">
                  <div>
                    <label className="text-sm font-medium">Current Password</label>
                    <div className="relative">
                      <Input
                        type={showPassword ? "text" : "password"}
                        value={currentPassword}
                        onChange={(e) => setCurrentPassword(e.target.value)}
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        className="absolute right-2 top-1/2 transform -translate-y-1/2"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <EyeNoneIcon className="w-4 h-4" /> : <EyeOpenIcon className="w-4 h-4" />}
                      </Button>
                    </div>
                  </div>
                  <div>
                    <label className="text-sm font-medium">New Password</label>
                    <Input
                      type="password"
                      value={newPassword}
                      onChange={(e) => setNewPassword(e.target.value)}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Confirm New Password</label>
                    <Input
                      type="password"
                      value={confirmPassword}
                      onChange={(e) => setConfirmPassword(e.target.value)}
                    />
                  </div>
                  <Button onClick={handlePasswordChange} className="bg-blue-600 hover:bg-blue-700">
                    Update Password
                  </Button>
                </div>
              </div>

              {/* Security Info */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="p-4 border rounded-lg">
                  <p className="text-sm text-slate-600">Last Password Change</p>
                  <p className="font-medium">{new Date(securityInfo.lastPasswordChange).toLocaleDateString()}</p>
                </div>
                <div className="p-4 border rounded-lg">
                  <p className="text-sm text-slate-600">Active Login Sessions</p>
                  <p className="font-medium">{securityInfo.loginSessions} sessions</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="billing" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <CreditCardIcon className="w-5 h-5" />
                Billing Information
              </CardTitle>
              <CardDescription>Manage your payment methods and billing preferences</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="text-center py-8">
                <CreditCardIcon className="w-12 h-12 text-slate-400 mx-auto mb-4" />
                <p className="text-slate-600">No billing information required</p>
                <p className="text-sm text-slate-500 mt-1">
                  Investment fees are deducted from returns automatically
                </p>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
