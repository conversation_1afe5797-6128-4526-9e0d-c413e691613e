"use client";

import React from "react";
import { motion } from "framer-motion";
import { useDashboard } from "@/contexts/DashboardContext";
import { signOut, useSession } from "next-auth/react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  HamburgerMenuIcon,
  BellIcon,
  PersonIcon,
  ExitIcon,
  GearIcon,
  QuestionMarkCircledIcon,
  ChevronDownIcon
} from "@radix-ui/react-icons";

export function DashboardHeader() {
  const { data: session } = useSession();
  const { state, toggleSidebar, setActiveView } = useDashboard();
  const { user, notifications, activeView } = state;
  const [dropdownOpen, setDropdownOpen] = React.useState(false);

  const userRole = session?.user?.role || "INVESTOR";
  const isAdmin = userRole === "ADMIN" || userRole === "SUPER_ADMIN";

  // Get unread notifications count with safety check
  const unreadCount = (notifications && Array.isArray(notifications))
    ? notifications.filter(n => n && typeof n === 'object' && !n.isRead).length
    : 0;

  // Get current view title
  const getViewTitle = (view: string) => {
    const titles = {
      dashboard: "Dashboard / Home",
      investments: "Investments / Projects", 
      "boss-ai-form": "Boss AI Investment Form",
      analytics: "Portfolio Analytics",
      documents: "Documents / KYC",
      notifications: "Notifications / Alerts",
      settings: "Account Settings",
      support: "Support / Help"
    };
    return titles[view as keyof typeof titles] || "Dashboard";
  };

  const handleSignOut = async () => {
    await signOut({ callbackUrl: "/" });
  };

  const handleNotificationClick = () => {
    setActiveView(isAdmin ? "admin-notifications" : "investor-notifications");
  };

  const handleSettingsClick = () => {
    setActiveView(isAdmin ? "admin-settings" : "investor-settings");
  };

  const handleSupportClick = () => {
    setActiveView(isAdmin ? "admin-support" : "investor-support");
  };

  return (
    <header className="bg-slate-900/95 backdrop-blur-xl shadow-lg border-b border-slate-700/50 z-30">
      <div className="flex items-center justify-between h-16 pl-0 pr-6 transform lg:-translate-x-1/4">
        {/* Left Section */}
        <div className="flex items-center space-x-6">
          {/* Mobile Menu Button */}
          <Button
            variant="ghost"
            size="sm"
            onClick={toggleSidebar}
            className="lg:hidden p-2 hover:bg-slate-700/50 text-slate-300 hover:text-white rounded-lg transition-colors"
          >
            <HamburgerMenuIcon className="h-5 w-5" />
          </Button>

          {/* Page Title */}
          <div>
            <h1 className="text-xl font-semibold text-white">
              {getViewTitle(activeView)}
            </h1>
            <p className="text-sm text-slate-400 hidden sm:block">
              Boss AI Investment Platform
            </p>
          </div>
        </div>

        {/* Right Section */}
        <div className="flex items-center space-x-4">
          {/* KYC Status Badge */}
          {user?.kycStatus && (
            <Badge
              variant={user.kycStatus === 'APPROVED' ? 'default' : 'secondary'}
              className={`hidden sm:inline-flex px-2 py-1 text-xs font-medium ${
                user.kycStatus === 'APPROVED'
                  ? 'bg-emerald-500/10 text-emerald-400 border-emerald-500/20'
                  : user.kycStatus === 'PENDING_REVIEW'
                  ? 'bg-amber-500/10 text-amber-400 border-amber-500/20'
                  : 'bg-slate-500/10 text-slate-400 border-slate-500/20'
              }`}
            >
              KYC: {user.kycStatus.replace('_', ' ')}
            </Badge>
          )}

          {/* Notifications */}
          <div className="relative">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleNotificationClick}
              className="relative p-2 hover:bg-slate-700/50 text-slate-300 hover:text-white rounded-lg transition-colors"
            >
              <BellIcon className="h-5 w-5" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium">
                  {unreadCount > 9 ? '9+' : unreadCount}
                </span>
              )}
            </Button>
          </div>

          {/* User Menu */}
          <DropdownMenu open={dropdownOpen} onOpenChange={setDropdownOpen}>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative flex items-center gap-2 h-9 px-2 rounded-full hover:bg-slate-700/50 transition-colors">
                <div className="h-8 w-8 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center">
                  {user?.image ? (
                    <img
                      src={user.image}
                      alt={user.name || "User"}
                      className="h-8 w-8 rounded-full object-cover"
                    />
                  ) : (
                    <span className="text-white font-medium text-sm">
                      {user?.firstName?.[0] || user?.name?.[0] || user?.email?.[0]?.toUpperCase() || "U"}
                    </span>
                  )}
                </div>
                <ChevronDownIcon
                  className={`h-4 w-4 text-slate-300 transition-transform duration-200 ${
                    dropdownOpen ? 'rotate-180' : ''
                  }`}
                />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end">
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">
                    {user?.firstName || user?.name || "User"}
                  </p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {user?.email}
                  </p>
                  <Badge variant="outline" className="w-fit text-xs">
                    {user?.role || "USER"}
                  </Badge>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSettingsClick}>
                <GearIcon className="mr-2 h-4 w-4" />
                <span>Account Settings</span>
              </DropdownMenuItem>
              <DropdownMenuItem onClick={handleSupportClick}>
                <QuestionMarkCircledIcon className="mr-2 h-4 w-4" />
                <span>Support & Help</span>
              </DropdownMenuItem>
              <DropdownMenuSeparator />
              <DropdownMenuItem onClick={handleSignOut}>
                <ExitIcon className="mr-2 h-4 w-4" />
                <span>Sign Out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </div>
    </header>
  );
}
