"use client";

import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { AIButton } from "@/components/ui/ai-button";
import { Badge } from "@/components/ui/badge";
import { UploadIcon, FileIcon, CheckIcon } from "@radix-ui/react-icons";

interface DocumentsKYCProps {
  user: any;
  userData: any;
}

export function DocumentsKYC({ user, userData }: DocumentsKYCProps) {
  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold gradient-text">Documents & KYC</h1>
        <p className="text-muted-foreground mt-2">Manage your identification documents and compliance status</p>
      </div>

      {/* KYC Status Overview */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>KYC Verification Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <div className={`w-4 h-4 rounded-full ${
                  userData.kycStatus === "APPROVED" ? "bg-green-500" : "bg-yellow-500"
                }`}></div>
                <span className={`font-semibold text-lg ${
                  userData.kycStatus === "APPROVED" ? "text-green-600" : "text-yellow-600"
                }`}>
                  {userData.kycStatus === "APPROVED" ? "Verified" : "Pending Verification"}
                </span>
              </div>
              
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Identity Verification</span>
                  <CheckIcon className="h-4 w-4 text-green-500" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Address Verification</span>
                  <CheckIcon className="h-4 w-4 text-green-500" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Financial Information</span>
                  <CheckIcon className="h-4 w-4 text-green-500" />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">AML Screening</span>
                  <CheckIcon className="h-4 w-4 text-green-500" />
                </div>
              </div>

              <div className="pt-4 border-t">
                <p className="text-sm text-muted-foreground">
                  {userData.kycStatus === "APPROVED" 
                    ? "Your KYC verification is complete. You can now make investments up to $1,000,000 per transaction."
                    : "Please upload the required documents to complete your KYC verification."
                  }
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Investment Limits</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm">Current Limit</span>
                <span className="font-semibold text-green-600">
                  {userData.kycStatus === "APPROVED" ? "$1,000,000" : "$0"}
                </span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Used This Month</span>
                <span className="font-semibold">$175,000</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm">Available</span>
                <span className="font-semibold text-blue-600">
                  {userData.kycStatus === "APPROVED" ? "$825,000" : "$0"}
                </span>
              </div>
              
              <div className="pt-4 border-t">
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full" 
                    style={{width: userData.kycStatus === "APPROVED" ? "17.5%" : "0%"}}
                  ></div>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  17.5% of monthly limit used
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Document Upload */}
      <Card>
        <CardHeader>
          <CardTitle>Document Upload</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-8 text-center hover:border-primary transition-colors">
            <UploadIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-lg font-medium mb-2">Upload Documents</p>
            <p className="text-muted-foreground mb-4">
              Drag and drop files here or click to browse
            </p>
            <p className="text-sm text-muted-foreground mb-4">
              Supported formats: PDF, JPG, PNG (Max 10MB per file)
            </p>
            <AIButton variant="outline">
              Choose Files
            </AIButton>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <h4 className="font-medium mb-3">Required Documents</h4>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">• Government-issued ID (Passport/Driver's License)</div>
                <div className="text-sm text-muted-foreground">• Proof of Address (Utility Bill/Bank Statement)</div>
                <div className="text-sm text-muted-foreground">• Bank Account Verification</div>
                <div className="text-sm text-muted-foreground">• Source of Funds Declaration</div>
              </div>
            </div>
            
            <div>
              <h4 className="font-medium mb-3">Optional Documents</h4>
              <div className="space-y-2">
                <div className="text-sm text-muted-foreground">• Tax Returns (Last 2 years)</div>
                <div className="text-sm text-muted-foreground">• Investment Experience Certificate</div>
                <div className="text-sm text-muted-foreground">• Professional References</div>
                <div className="text-sm text-muted-foreground">• Additional Income Proof</div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Uploaded Documents */}
      <Card>
        <CardHeader>
          <CardTitle>Uploaded Documents</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center space-x-3">
                <FileIcon className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium">passport_scan.pdf</p>
                  <p className="text-sm text-muted-foreground">Uploaded: March 10, 2024</p>
                </div>
              </div>
              <Badge className="bg-green-100 text-green-800">Verified</Badge>
            </div>
            
            <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center space-x-3">
                <FileIcon className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium">bank_statement.pdf</p>
                  <p className="text-sm text-muted-foreground">Uploaded: March 10, 2024</p>
                </div>
              </div>
              <Badge className="bg-green-100 text-green-800">Verified</Badge>
            </div>
            
            <div className="flex items-center justify-between p-4 bg-green-50 rounded-lg border border-green-200">
              <div className="flex items-center space-x-3">
                <FileIcon className="h-5 w-5 text-green-600" />
                <div>
                  <p className="font-medium">proof_of_address.pdf</p>
                  <p className="text-sm text-muted-foreground">Uploaded: March 10, 2024</p>
                </div>
              </div>
              <Badge className="bg-green-100 text-green-800">Verified</Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
