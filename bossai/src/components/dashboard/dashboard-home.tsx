"use client";

import { motion } from "framer-motion";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { TriangleUpIcon, DashIcon, ClockIcon, TargetIcon } from "@radix-ui/react-icons";

interface DashboardHomeProps {
  user: any;
  userData: any;
  stats: any;
}

export function DashboardHome({ user, userData, stats }: DashboardHomeProps) {
  const portfolioData = [
    { month: "Jan", value: 45000 },
    { month: "Feb", value: 52000 },
    { month: "Mar", value: 48000 },
    { month: "Apr", value: 61000 },
    { month: "May", value: 55000 },
    { month: "Jun", value: 67000 },
  ];

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 },
  };

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Welcome Header */}
      <motion.div variants={itemVariants} className="mb-8">
        <h1 className="text-3xl font-bold gradient-text mb-2">
          Welcome back, {userData.firstName || userData.name || "Investor"}
        </h1>
        <p className="text-muted-foreground text-lg">
          Here's your investment portfolio overview
        </p>
      </motion.div>

      {/* Key Metrics Grid */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        {/* Total Invested */}
        <Card className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Invested</CardTitle>
            <DashIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              ${ (stats?.totalInvested || 0).toLocaleString() }
            </div>
            <p className="text-xs text-muted-foreground">
              {(stats?.monthlyGrowth ?? 0) >= 0 ? '+' : ''}{(stats?.monthlyGrowth ?? 0).toFixed(1)}% from last month
            </p>
          </CardContent>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-green-500 to-emerald-500" />
        </Card>

        {/* Active Projects */}
        <Card className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Active Projects</CardTitle>
            <TargetIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {stats?.activeInvestments ?? 0}
            </div>
            <p className="text-xs text-muted-foreground">
              {(stats?.pendingInvestments ?? 0)} pending
            </p>
          </CardContent>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 to-cyan-500" />
        </Card>

        {/* Pending Approvals */}
        <Card className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Approvals</CardTitle>
            <ClockIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">
              {stats?.pendingInvestments ?? 0}
            </div>
            <p className="text-xs text-muted-foreground">
              Pending investments
            </p>
          </CardContent>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-yellow-500 to-orange-500" />
        </Card>

        {/* Portfolio Growth */}
        <Card className="relative overflow-hidden">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Portfolio Growth</CardTitle>
            <TriangleUpIcon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600">
              {(stats?.portfolioGrowth ?? 0) >= 0 ? '+' : ''}{(stats?.portfolioGrowth ?? 0).toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              YTD performance
            </p>
          </CardContent>
          <div className="absolute bottom-0 left-0 w-full h-1 bg-gradient-to-r from-purple-500 to-pink-500" />
        </Card>
      </motion.div>

      {/* Portfolio Growth Chart & Top Performer */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Portfolio Growth Chart */}
        <Card className="lg:col-span-2">
          <CardHeader>
            <CardTitle>Portfolio Growth</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="h-64 flex items-end justify-between space-x-2">
              {portfolioData.map((data, index) => (
                <div key={data.month} className="flex flex-col items-center space-y-2 flex-1">
                  <motion.div
                    initial={{ height: 0 }}
                    animate={{ height: `${(data.value / 70000) * 100}%` }}
                    transition={{ delay: index * 0.1, duration: 0.6 }}
                    className="w-full bg-gradient-to-t from-blue-500 to-purple-500 rounded-t-md min-h-[20px]"
                  />
                  <span className="text-xs text-muted-foreground">{data.month}</span>
                  <span className="text-xs font-medium">${(data.value / 1000).toFixed(0)}k</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Top Performer */}
        <Card>
          <CardHeader>
            <CardTitle>Top Performer</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-r from-green-500 to-emerald-500 rounded-lg flex items-center justify-center text-white font-bold">
                AI
              </div>
              <div>
                <h3 className="font-semibold">NeuralTech AI</h3>
                <p className="text-sm text-muted-foreground">Artificial Intelligence</p>
              </div>
            </div>
            
            <div className="space-y-2">
              <div className="flex justify-between">
                <span className="text-sm">Investment</span>
                <span className="font-medium">$15,000</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Current Value</span>
                <span className="font-medium text-green-600">$22,500</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">ROI</span>
                <span className="font-bold text-green-600">+50%</span>
              </div>
            </div>

            <div className="pt-4">
              <div className="flex justify-between text-sm mb-2">
                <span>Progress to next milestone</span>
                <span>75%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <motion.div
                  initial={{ width: 0 }}
                  animate={{ width: "75%" }}
                  transition={{ delay: 0.5, duration: 1 }}
                  className="bg-gradient-to-r from-green-500 to-emerald-500 h-2 rounded-full"
                />
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Recent Activity */}
      <motion.div variants={itemVariants}>
        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {[
                {
                  action: "Investment Approved",
                  project: "GreenEnergy Solutions",
                  amount: "$25,000",
                  time: "2 hours ago",
                  status: "success",
                },
                {
                  action: "Dividend Received",
                  project: "TechStartup Inc",
                  amount: "+$1,250",
                  time: "1 day ago",
                  status: "success",
                },
                {
                  action: "KYC Document Pending",
                  project: "Account Verification",
                  amount: "",
                  time: "3 days ago",
                  status: "warning",
                },
              ].map((activity, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-3 rounded-lg bg-muted/30"
                >
                  <div className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${
                      activity.status === "success" ? "bg-green-500" :
                      activity.status === "warning" ? "bg-yellow-500" : "bg-gray-500"
                    }`} />
                    <div>
                      <p className="font-medium">{activity.action}</p>
                      <p className="text-sm text-muted-foreground">{activity.project}</p>
                    </div>
                  </div>
                  <div className="text-right">
                    {activity.amount && (
                      <p className={`font-medium ${
                        activity.amount.startsWith("+") ? "text-green-600" : ""
                      }`}>
                        {activity.amount}
                      </p>
                    )}
                    <p className="text-xs text-muted-foreground">{activity.time}</p>
                  </div>
                </motion.div>
              ))}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </motion.div>
  );
}
