"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Section } from "@/components/ui/section";
import { Container } from "@/components/ui/container";
import { AIButton } from "@/components/ui/ai-button";
import { FloatingOrbs } from "@/components/ui/floating-orbs";
import { ctaData } from "@/constants/data";
import { easing } from "@/lib/utils";

const CTA: React.FC = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: easing.easeOut
      }
    }
  };

  const buttonVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easing.easeOut
      }
    }
  };

  return (
    <Section 
      background="pattern" 
      padding="xl" 
      className="gradient-bg"
    >
      {/* Floating Orbs */}
      <FloatingOrbs count={3} />
      
      <Container className="text-center relative z-10">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          {/* Main Quote */}
          <motion.h2
            variants={itemVariants}
            className="text-5xl md:text-7xl font-black mb-8 glow-text leading-tight"
          >
            &ldquo;The future doesn&apos;t belong
            <br />
            to those who wait.
            <br />
            <span className="gradient-text">
              It belongs to those who build.&rdquo;
            </span>
          </motion.h2>
          
          {/* Subtitle */}
          <motion.p
            variants={itemVariants}
            className="text-2xl text-gray-300 mb-8"
          >
            {ctaData.subtitle}
          </motion.p>
          
          {/* Description */}
          <motion.p
            variants={itemVariants}
            className="text-xl text-gray-400 mb-16"
          >
            {ctaData.description}
          </motion.p>
          
          {/* CTA Buttons */}
          <motion.div
            variants={containerVariants}
            className="flex flex-col sm:flex-row gap-6 justify-center items-center"
          >
            <motion.div variants={buttonVariants}>
              <AIButton 
                variant="primary" 
                size="lg"
                className="text-lg gap-3"
              >
                {ctaData.primaryCTA}
              </AIButton>
            </motion.div>
            
            <motion.div variants={buttonVariants}>
              <AIButton 
                variant="secondary" 
                size="lg"
                className="text-lg"
              >
                {ctaData.secondaryCTA}
              </AIButton>
            </motion.div>
          </motion.div>
        </motion.div>
      </Container>
    </Section>
  );
};

export { CTA };
