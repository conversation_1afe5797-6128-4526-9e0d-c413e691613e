"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { TrendingUp, Users, Eye, Heart } from "lucide-react";
import { Section } from "@/components/ui/section";
import { Container } from "@/components/ui/container";
import { partnershipData } from "@/constants/data";
import { easing } from "@/lib/utils";

interface PartnershipProps {
  id?: string;
}

const Partnership: React.FC<PartnershipProps> = ({ id }) => {
  const icons = {
    "shared-growth": TrendingUp,
    "collaboration": Users,
    "vision": Eye,
    "impact": Heart
  };

  const colors = {
    "shared-growth": "text-blue-400",
    "collaboration": "text-purple-400",
    "vision": "text-green-400",
    "impact": "text-yellow-400"
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easing.easeOut
      }
    }
  };

  const iconVariants = {
    hidden: { opacity: 0, scale: 0 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: easing.easeOut
      }
    },
    hover: {
      scale: 1.1,
      rotate: 5,
      transition: {
        duration: 0.2,
        ease: easing.easeOut
      }
    }
  };

  return (
    <Section id={id} background="dark" padding="lg">
      <Container>
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-section mb-8">{partnershipData.title}</h2>
            <p className="text-xl text-gray-300">{partnershipData.subtitle}</p>
          </motion.div>
          
          {/* Benefits Grid */}
          <motion.div 
            variants={containerVariants}
            className="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16"
          >
            {partnershipData.benefits.map((benefit) => {
              const IconComponent = icons[benefit.id as keyof typeof icons];
              const iconColor = colors[benefit.id as keyof typeof colors];
              
              return (
                <motion.div
                  key={benefit.id}
                  variants={itemVariants}
                  className="text-center"
                >
                  <motion.div
                    variants={iconVariants}
                    whileHover="hover"
                    className={`w-20 h-20 bg-gradient-to-br ${benefit.iconBg} rounded-full flex items-center justify-center mx-auto mb-6 cursor-pointer`}
                  >
                    <IconComponent className="w-10 h-10 text-white" />
                  </motion.div>
                  <h3 className={`text-2xl font-bold mb-3 ${iconColor}`}>
                    {benefit.title}
                  </h3>
                  <p className="text-gray-300">{benefit.description}</p>
                </motion.div>
              );
            })}
          </motion.div>
          
          {/* Bottom Text */}
          <motion.div variants={itemVariants} className="text-center">
            <p className="text-2xl font-light text-gray-300">
              {partnershipData.bottomText.normal}{" "}
              <span className="text-white font-semibold">
                {partnershipData.bottomText.bold}
              </span>
            </p>
          </motion.div>
        </motion.div>
      </Container>
    </Section>
  );
};

export { Partnership };
