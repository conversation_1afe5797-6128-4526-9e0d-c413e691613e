"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { CheckCircle } from "lucide-react";
import { Section } from "@/components/ui/section";
import { Container } from "@/components/ui/container";
import { storyData } from "@/constants/data";
import { easing } from "@/lib/utils";

interface StoryProps {
  id?: string;
}

const Story: React.FC<StoryProps> = ({ id }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.3,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -50 },
    visible: {
      opacity: 1,
      x: 0,
      transition: {
        duration: 0.8,
        ease: easing.easeOut
      }
    }
  };

  const imageVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 1,
        ease: easing.easeOut
      }
    }
  };

  return (
    <Section id={id} padding="lg">
      {/* Neural Network Background */}
      <div className="neural-network" />
      
      <Container>
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
          className="grid md:grid-cols-2 gap-16 items-center"
        >
          {/* Visual Element */}
          <motion.div
            variants={imageVariants}
            className="relative"
          >
            <div className="w-full h-96 bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-3xl flex items-center justify-center relative overflow-hidden">
              <CheckCircle className="w-64 h-64 text-blue-400 opacity-60" />
              
              {/* Animated Shimmer Effect */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12"
                animate={{
                  x: [-100, 500],
                }}
                transition={{
                  duration: 3,
                  repeat: Infinity,
                  repeatDelay: 2,
                  ease: "easeInOut"
                }}
              />
            </div>
          </motion.div>
          
          {/* Content */}
          <motion.div variants={itemVariants}>
            <motion.h2
              variants={itemVariants}
              className="text-section mb-8 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent"
            >
              {storyData.title}
            </motion.h2>
            
            <div className="space-y-6 text-lg text-gray-300 leading-relaxed">
              {storyData.paragraphs.map((paragraph, index) => (
                <motion.p
                  key={index}
                  variants={itemVariants}
                  dangerouslySetInnerHTML={{
                    __html: paragraph
                      .replace(/\*\*(.*?)\*\*/g, '<strong class="text-white">$1</strong>')
                      .replace(/That catalyst is our intelligence\./g, '<strong class="text-blue-400">That catalyst is our intelligence.</strong>')
                      .replace(/true partners in creation\./g, '<strong class="text-purple-400">true partners in creation.</strong>')
                  }}
                />
              ))}
            </div>
          </motion.div>
        </motion.div>
      </Container>
    </Section>
  );
};

export { Story };
