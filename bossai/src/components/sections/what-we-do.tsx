"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Search, Zap, Users } from "lucide-react";
import { Section } from "@/components/ui/section";
import { Container } from "@/components/ui/container";
import { Card, CardContent } from "@/components/ui/card";
import { whatWeDoData } from "@/constants/data";
import { easing } from "@/lib/utils";

interface WhatWeDoProps {
  id?: string;
}

const WhatWeDo: React.FC<WhatWeDoProps> = ({ id }) => {
  const icons = {
    identify: Search,
    embed: Zap,
    share: Users
  };

  const iconGradients = {
    identify: "from-blue-500 to-purple-500",
    embed: "from-purple-500 to-green-500",
    share: "from-green-500 to-blue-500"
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easing.easeOut
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easing.easeOut
      }
    },
    hover: {
      y: -8,
      transition: {
        duration: 0.3,
        ease: easing.easeOut
      }
    }
  };

  return (
    <Section id={id} background="dark" padding="lg">
      <Container>
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-section mb-6">{whatWeDoData.title}</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              {whatWeDoData.subtitle}
            </p>
          </motion.div>
          
          {/* Cards Grid */}
          <motion.div 
            variants={containerVariants}
            className="grid md:grid-cols-3 gap-8 mb-16"
          >
            {whatWeDoData.cards.map((card) => {
              const IconComponent = icons[card.id as keyof typeof icons];
              const iconGradient = iconGradients[card.id as keyof typeof iconGradients];
              
              return (
                <motion.div
                  key={card.id}
                  variants={cardVariants}
                  whileHover="hover"
                >
                  <Card className={`card-hover bg-gradient-to-br ${card.gradient} p-8 border ${card.borderColor} h-full`}>
                    <CardContent className="p-0">
                      <div className={`w-16 h-16 bg-gradient-to-br ${iconGradient} rounded-xl flex items-center justify-center mb-6`}>
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      <h3 className="text-2xl font-bold mb-4">{card.title}</h3>
                      <p className="text-gray-300">{card.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </motion.div>
          
          {/* Bottom Text */}
          <motion.div variants={itemVariants} className="text-center">
            <p className="text-2xl font-light text-gray-300 mb-4">
              {whatWeDoData.bottomText.light}
            </p>
            <p className="text-3xl font-bold gradient-text">
              {whatWeDoData.bottomText.bold}
            </p>
          </motion.div>
        </motion.div>
      </Container>
    </Section>
  );
};

export { WhatWeDo };
