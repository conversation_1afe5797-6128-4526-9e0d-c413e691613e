"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Section } from "@/components/ui/section";
import { Container } from "@/components/ui/container";
import { AIButton } from "@/components/ui/ai-button";
import { FloatingOrbs } from "@/components/ui/floating-orbs";
import { heroData } from "@/constants/data";
import { easing } from "@/lib/utils";

const Hero: React.FC = () => {
  const titleVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        ease: easing.easeOut
      }
    }
  };

  const descriptionVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.8,
        delay: 0.3,
        ease: easing.easeOut
      }
    }
  };

  const buttonVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        delay: 0.6,
        ease: easing.easeOut
      }
    }
  };

  return (
    <Section 
      background="pattern" 
      padding="xl" 
      className="min-h-screen flex items-center justify-center gradient-bg"
    >
      {/* Floating Orbs */}
      <FloatingOrbs count={3} />
      
      <Container className="text-center relative z-10">
        <motion.div
          initial="hidden"
          animate="visible"
          className="space-y-8"
        >
          {/* Main Title */}
          <motion.h1
            variants={titleVariants}
            className="text-display glow-text leading-tight"
          >
            Where Vision
            <br />
            <span className="gradient-text">
              Meets Intelligence
            </span>
          </motion.h1>
          
          {/* Description */}
          <motion.p
            variants={descriptionVariants}
            className="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto leading-relaxed font-light"
          >
            {heroData.description}
          </motion.p>
          
          {/* CTA Button */}
          <motion.div
            variants={buttonVariants}
            className="flex justify-center"
          >
            <AIButton 
              variant="primary" 
              size="lg"
              className="text-xl gap-3"
            >
              {heroData.primaryCTA}
            </AIButton>
          </motion.div>
        </motion.div>
      </Container>
    </Section>
  );
};

export { Hero };
