"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Section } from "@/components/ui/section";
import { Container } from "@/components/ui/container";
import { Card, CardContent } from "@/components/ui/card";
import { tomorrowData } from "@/constants/data";
import { easing } from "@/lib/utils";

interface TomorrowProps {
  id?: string;
}

const Tomorrow: React.FC<TomorrowProps> = ({ id }) => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easing.easeOut
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, scale: 0.9 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: {
        duration: 0.5,
        ease: easing.easeOut
      }
    },
    hover: {
      scale: 1.05,
      transition: {
        duration: 0.2,
        ease: easing.easeOut
      }
    }
  };

  return (
    <Section id={id} background="grid" padding="lg" className="relative">
      {/* Gradient Overlay */}
      <div className="absolute inset-0 bg-gradient-to-r from-blue-900/20 via-purple-900/20 to-green-900/20" />
      
      <Container className="relative z-10">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-6xl font-bold mb-8 glow-text">
              {tomorrowData.title}
            </h2>
            <p className="text-2xl text-gray-300 max-w-2xl mx-auto mb-12">
              {tomorrowData.subtitle}
            </p>
            <p className="text-xl text-gray-400 max-w-4xl mx-auto">
              {tomorrowData.description}
            </p>
          </motion.div>
          
          {/* Industry Cards */}
          <motion.div 
            variants={containerVariants}
            className="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16"
          >
            {tomorrowData.industries.map((industry) => (
              <motion.div
                key={industry.id}
                variants={cardVariants}
                whileHover="hover"
              >
                <Card className={`bg-gradient-to-br ${industry.gradient} p-6 border ${industry.borderColor} cursor-pointer`}>
                  <CardContent className="p-0">
                    <h3 className={`text-xl font-bold text-${industry.color} mb-2`}>
                      {industry.title}
                    </h3>
                    <p className="text-gray-300">{industry.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
          
          {/* Bottom Text */}
          <motion.div variants={itemVariants} className="text-center">
            <p className="text-2xl font-light text-gray-300">
              {tomorrowData.bottomText}
            </p>
          </motion.div>
        </motion.div>
      </Container>
    </Section>
  );
};

export { Tomorrow };
