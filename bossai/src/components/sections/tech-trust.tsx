"use client";

import * as React from "react";
import { motion } from "framer-motion";
import { Shield, BarChart3, Settings } from "lucide-react";
import { Section } from "@/components/ui/section";
import { Container } from "@/components/ui/container";
import { Card, CardContent } from "@/components/ui/card";
import { techTrustData } from "@/constants/data";
import { easing } from "@/lib/utils";

const TechTrust: React.FC = () => {
  const icons = {
    protection: Shield,
    infrastructure: BarChart3,
    solutions: Settings
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2,
        delayChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 50 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easing.easeOut
      }
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easing.easeOut
      }
    },
    hover: {
      y: -5,
      transition: {
        duration: 0.3,
        ease: easing.easeOut
      }
    }
  };

  return (
    <Section padding="lg" className="relative">
      {/* Neural Network Background */}
      <div className="neural-network" />
      
      <Container className="relative z-10">
        <motion.div
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          variants={containerVariants}
        >
          {/* Header */}
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-section mb-8">{techTrustData.title}</h2>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              {techTrustData.subtitle}
            </p>
          </motion.div>
          
          {/* Features Grid */}
          <motion.div 
            variants={containerVariants}
            className="grid md:grid-cols-3 gap-8 mb-12"
          >
            {techTrustData.features.map((feature) => {
              const IconComponent = icons[feature.id as keyof typeof icons];
              
              return (
                <motion.div
                  key={feature.id}
                  variants={cardVariants}
                  whileHover="hover"
                >
                  <Card className="bg-gradient-to-br from-gray-900/50 to-blue-900/30 p-8 border border-gray-700/50 h-full">
                    <CardContent className="p-0">
                      <div className={`w-12 h-12 ${feature.iconColor} rounded-lg flex items-center justify-center mb-6`}>
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <h3 className="text-xl font-bold mb-3">{feature.title}</h3>
                      <p className="text-gray-300">{feature.description}</p>
                    </CardContent>
                  </Card>
                </motion.div>
              );
            })}
          </motion.div>
          
          {/* Bottom Text */}
          <motion.div variants={itemVariants} className="text-center">
            <p className="text-2xl font-light text-gray-300">
              {techTrustData.bottomText.normal}{" "}
              <span className="text-white font-semibold">
                {techTrustData.bottomText.bold}
              </span>
            </p>
          </motion.div>
        </motion.div>
      </Container>
    </Section>
  );
};

export { TechTrust };
