"use client";

import { useState } from "react";
import { motion } from "framer-motion";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>H<PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { AIButton } from "@/components/ui/ai-button";
import { easing } from "@/lib/utils";

interface AdminDashboardProps {
  users: any[];
  investments: any[];
  documents: any[];
  auditLogs: any[];
  stats: {
    totalUsers: number;
    totalInvestments: number;
    pendingInvestments: number;
    totalInvestmentValue: { _sum: { amount: any } };
    pendingKYC: number;
  };
  currentUser: any;
}

export function AdminDashboard({
  users,
  investments,
  documents,
  auditLogs,
  stats,
  currentUser,
}: AdminDashboardProps) {
  const [activeTab, setActiveTab] = useState("overview");

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        ease: easing.easeOut,
      },
    },
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easing.easeOut,
      },
    },
  };

  const formatCurrency = (amount: any) => {
    const value = Number(amount) || 0;
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(value);
  };

  const tabs = [
    { id: "overview", label: "Overview" },
    { id: "users", label: "Users" },
    { id: "investments", label: "Investments" },
    { id: "documents", label: "Documents" },
    { id: "audit", label: "Audit Log" },
  ];

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Stats Overview */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Users</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalUsers ?? 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Investments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats?.totalInvestments ?? 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Pending Investments</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-500">{stats?.pendingInvestments ?? 0}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total Value</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{formatCurrency(stats?.totalInvestmentValue?._sum?.amount ?? 0)}</div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Pending KYC</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-500">{stats?.pendingKYC ?? 0}</div>
          </CardContent>
        </Card>
      </motion.div>

      {/* Tab Navigation */}
      <motion.div variants={itemVariants}>
        <div className="border-b border-border">
          <nav className="flex space-x-8">
            {tabs.map((tab) => (
              <button
                key={tab.id}
                onClick={() => setActiveTab(tab.id)}
                className={`py-2 px-1 border-b-2 font-medium text-sm ${
                  activeTab === tab.id
                    ? "border-primary text-primary"
                    : "border-transparent text-muted-foreground hover:text-foreground"
                }`}
              >
                {tab.label}
              </button>
            ))}
          </nav>
        </div>
      </motion.div>

      {/* Tab Content */}
      <motion.div variants={itemVariants}>
        {activeTab === "overview" && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Recent Users */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Users</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {users.slice(0, 5).map((user) => (
                    <div key={user.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{user.name || user.email}</p>
                        <p className="text-sm text-muted-foreground">{user.email}</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm">{user.role}</p>
                        <p className="text-xs text-muted-foreground">{user.kycStatus}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Investments */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Investments</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {investments.slice(0, 5).map((investment) => (
                    <div key={investment.id} className="flex items-center justify-between">
                      <div>
                        <p className="font-medium">{investment.type}</p>
                        <p className="text-sm text-muted-foreground">
                          {investment.user.firstName || investment.user.name || investment.user.email}
                        </p>
                      </div>
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(investment.amount)}</p>
                        <p className={`text-sm ${
                          investment.status === "APPROVED" ? "text-green-500" :
                          investment.status === "PENDING" ? "text-yellow-500" :
                          "text-red-500"
                        }`}>
                          {investment.status}
                        </p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === "users" && (
          <Card>
            <CardHeader>
              <CardTitle>User Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {users.map((user) => (
                  <div key={user.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">{user.name || "No name"}</p>
                      <p className="text-sm text-muted-foreground">{user.email}</p>
                      <p className="text-xs text-muted-foreground">
                        Joined: {new Date(user.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="text-sm font-medium">{user.role}</p>
                        <p className="text-xs text-muted-foreground">{user.kycStatus}</p>
                        <p className="text-xs text-muted-foreground">
                          {user.investments.length} investments
                        </p>
                      </div>
                      <div className="space-x-2">
                        <AIButton size="sm" variant="outline">
                          View
                        </AIButton>
                        <AIButton size="sm" variant="outline">
                          Edit
                        </AIButton>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {activeTab === "investments" && (
          <Card>
            <CardHeader>
              <CardTitle>Investment Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {investments.map((investment) => (
                  <div key={investment.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div>
                      <p className="font-medium">{investment.type}</p>
                      <p className="text-sm text-muted-foreground">
                        {investment.user.firstName || investment.user.name || investment.user.email}
                      </p>
                      <p className="text-xs text-muted-foreground">
                        Submitted: {new Date(investment.createdAt).toLocaleDateString()}
                      </p>
                    </div>
                    <div className="flex items-center space-x-4">
                      <div className="text-right">
                        <p className="font-medium">{formatCurrency(investment.amount)}</p>
                        <p className={`text-sm ${
                          investment.status === "APPROVED" ? "text-green-500" :
                          investment.status === "PENDING" ? "text-yellow-500" :
                          "text-red-500"
                        }`}>
                          {investment.status}
                        </p>
                      </div>
                      <div className="space-x-2">
                        <AIButton size="sm" variant="outline">
                          Review
                        </AIButton>
                        {investment.status === "PENDING" && (
                          <>
                            <AIButton size="sm" variant="outline">
                              Approve
                            </AIButton>
                            <AIButton size="sm" variant="outline">
                              Reject
                            </AIButton>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </motion.div>
    </motion.div>
  );
}
