"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AIButton } from "@/components/ui/ai-button";
import { easing } from "@/lib/utils";

// Investment form validation schema
const investmentSchema = z.object({
  type: z.enum(["EQUITY", "DEBT", "HYBRID", "REAL_ESTATE", "CRYPTOCURRENCY", "COMMODITIES", "OTHER"]),
  amount: z.number().min(1000, "Minimum investment is $1,000").max(10000000, "Maximum investment is $10,000,000"),
  currency: z.string(),
  riskLevel: z.enum(["CONSERVATIVE", "MODERATE", "AGGRESSIVE", "VERY_AGGRESSIVE"]),
  duration: z.number().min(1).max(120).optional(),
  description: z.string().min(10, "Please provide a detailed description").max(1000),
  expectedReturn: z.number().min(0).max(100).optional(),
  
  // KYC and Personal Information
  firstName: z.string().min(2, "First name is required"),
  lastName: z.string().min(2, "Last name is required"),
  phone: z.string().min(10, "Valid phone number is required"),
  dateOfBirth: z.string(),
  address: z.string().min(10, "Complete address is required"),
  city: z.string().min(2, "City is required"),
  state: z.string().min(2, "State is required"),
  zipCode: z.string().min(5, "Valid ZIP code is required"),
  country: z.string().min(2, "Country is required"),
  
  // Investment Experience
  investmentExperience: z.enum(["BEGINNER", "INTERMEDIATE", "ADVANCED", "PROFESSIONAL"]),
  annualIncome: z.enum(["UNDER_50K", "50K_100K", "100K_250K", "250K_500K", "500K_1M", "OVER_1M"]),
  netWorth: z.enum(["UNDER_100K", "100K_500K", "500K_1M", "1M_5M", "5M_10M", "OVER_10M"]),
  
  // Legal and Compliance
  accreditedInvestor: z.boolean(),
  agreeToTerms: z.boolean().refine(val => val === true, "You must agree to the terms"),
  agreeToPrivacy: z.boolean().refine(val => val === true, "You must agree to the privacy policy"),
});

type InvestmentFormData = z.infer<typeof investmentSchema>;

export function InvestmentForm() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState(1);
  const totalSteps = 4;

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<InvestmentFormData>({
    resolver: zodResolver(investmentSchema),
    defaultValues: {
      currency: "USD",
      agreeToTerms: false,
      agreeToPrivacy: false,
      accreditedInvestor: false,
    },
  });

  const onSubmit = async (data: InvestmentFormData) => {
    setIsSubmitting(true);
    try {
      const response = await fetch("/api/investments", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        // Handle success
        alert("Investment application submitted successfully!");
      } else {
        throw new Error("Failed to submit application");
      }
    } catch (error) {
      console.error("Error submitting investment:", error);
      alert("Error submitting application. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  const nextStep = () => {
    if (currentStep < totalSteps) {
      setCurrentStep(currentStep + 1);
    }
  };

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1);
    }
  };

  const formVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easing.easeOut,
      },
    },
  };

  return (
    <motion.div
      variants={formVariants}
      initial="hidden"
      animate="visible"
      className="space-y-8"
    >
      {/* Progress Indicator */}
      <div className="flex items-center justify-between mb-8">
        {Array.from({ length: totalSteps }, (_, i) => (
          <div
            key={i}
            className={`flex items-center ${i < totalSteps - 1 ? "flex-1" : ""}`}
          >
            <div
              className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-semibold ${
                i + 1 <= currentStep
                  ? "bg-primary text-primary-foreground"
                  : "bg-muted text-muted-foreground"
              }`}
            >
              {i + 1}
            </div>
            {i < totalSteps - 1 && (
              <div
                className={`flex-1 h-1 mx-4 ${
                  i + 1 < currentStep ? "bg-primary" : "bg-muted"
                }`}
              />
            )}
          </div>
        ))}
      </div>

      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Step 1: Investment Details */}
        {currentStep === 1 && (
          <Card>
            <CardHeader>
              <CardTitle>Investment Details</CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Investment Type */}
              <div>
                <label className="block text-sm font-medium mb-2">Investment Type</label>
                <select
                  {...register("type")}
                  className="w-full p-3 border border-input rounded-lg bg-background"
                >
                  <option value="">Select investment type</option>
                  <option value="EQUITY">Equity</option>
                  <option value="DEBT">Debt</option>
                  <option value="HYBRID">Hybrid</option>
                  <option value="REAL_ESTATE">Real Estate</option>
                  <option value="CRYPTOCURRENCY">Cryptocurrency</option>
                  <option value="COMMODITIES">Commodities</option>
                  <option value="OTHER">Other</option>
                </select>
                {errors.type && (
                  <p className="text-destructive text-sm mt-1">{errors.type.message}</p>
                )}
              </div>

              {/* Investment Amount */}
              <div>
                <label className="block text-sm font-medium mb-2">Investment Amount (USD)</label>
                <input
                  type="number"
                  {...register("amount", { valueAsNumber: true })}
                  className="w-full p-3 border border-input rounded-lg bg-background"
                  placeholder="Enter amount"
                />
                {errors.amount && (
                  <p className="text-destructive text-sm mt-1">{errors.amount.message}</p>
                )}
              </div>

              {/* Risk Level */}
              <div>
                <label className="block text-sm font-medium mb-2">Risk Tolerance</label>
                <select
                  {...register("riskLevel")}
                  className="w-full p-3 border border-input rounded-lg bg-background"
                >
                  <option value="">Select risk level</option>
                  <option value="CONSERVATIVE">Conservative</option>
                  <option value="MODERATE">Moderate</option>
                  <option value="AGGRESSIVE">Aggressive</option>
                  <option value="VERY_AGGRESSIVE">Very Aggressive</option>
                </select>
                {errors.riskLevel && (
                  <p className="text-destructive text-sm mt-1">{errors.riskLevel.message}</p>
                )}
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium mb-2">Investment Description</label>
                <textarea
                  {...register("description")}
                  rows={4}
                  className="w-full p-3 border border-input rounded-lg bg-background"
                  placeholder="Describe your investment goals and strategy..."
                />
                {errors.description && (
                  <p className="text-destructive text-sm mt-1">{errors.description.message}</p>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Navigation Buttons */}
        <div className="flex justify-between">
          <AIButton
            type="button"
            variant="outline"
            onClick={prevStep}
            disabled={currentStep === 1}
          >
            Previous
          </AIButton>
          
          {currentStep < totalSteps ? (
            <AIButton type="button" onClick={nextStep}>
              Next Step
            </AIButton>
          ) : (
            <AIButton type="submit" loading={isSubmitting}>
              Submit Application
            </AIButton>
          )}
        </div>
      </form>
    </motion.div>
  );
}
