import Script from "next/script";

const StructuredData = () => {
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": "AI Venture Platform",
    "description": "We forge alliances that transform strong products into market leaders through carefully chosen Joint Ventures, bringing intelligence and innovation together.",
    "url": "https://aiventure.com",
    "logo": "https://aiventure.com/logo.png",
    "sameAs": [
      "https://linkedin.com/company/aiventure",
      "https://twitter.com/aiventure"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "Business Development",
      "email": "<EMAIL>"
    }
  };

  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "AI Venture Platform",
    "url": "https://aiventure.com",
    "description": "Transform your product into a market leader with our AI-powered joint ventures.",
    "potentialAction": {
      "@type": "SearchAction",
      "target": "https://aiventure.com/search?q={search_term_string}",
      "query-input": "required name=search_term_string"
    }
  };

  const serviceSchema = {
    "@context": "https://schema.org",
    "@type": "Service",
    "name": "AI Joint Venture Services",
    "description": "We identify products with untapped growth potential, embed our AI to enhance performance, and share the journey as equal partners.",
    "provider": {
      "@type": "Organization",
      "name": "AI Venture Platform"
    },
    "serviceType": "AI Integration and Joint Venture Services",
    "areaServed": "Worldwide"
  };

  return (
    <>
      <Script
        id="organization-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(organizationSchema)
        }}
      />
      <Script
        id="website-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(websiteSchema)
        }}
      />
      <Script
        id="service-schema"
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(serviceSchema)
        }}
      />
    </>
  );
};

export { StructuredData };
