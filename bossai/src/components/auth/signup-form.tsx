"use client";

import { useState } from "react";
import { signIn } from "next-auth/react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { motion } from "framer-motion";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { AIButton } from "@/components/ui/ai-button";
import { easing } from "@/lib/utils";

const signUpSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Please enter a valid email address"),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/, 
      "Password must contain uppercase, lowercase, number, and special character"),
  confirmPassword: z.string(),
  agreeToTerms: z.boolean().refine(val => val === true, "You must agree to the terms"),
  agreeToPrivacy: z.boolean().refine(val => val === true, "You must agree to the privacy policy"),
  agreeToMarketing: z.boolean().optional(),
}).refine((data) => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

type SignUpFormData = z.infer<typeof signUpSchema>;

interface SignUpFormProps {
  callbackUrl?: string;
}

export function SignUpForm({ callbackUrl }: SignUpFormProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<SignUpFormData>({
    resolver: zodResolver(signUpSchema),
  });

  const onSubmit = async (data: SignUpFormData) => {
    setIsLoading(true);
    setError(null);

    try {
      // Create account via API
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          firstName: data.firstName,
          lastName: data.lastName,
          email: data.email,
          password: data.password,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create account");
      }

      setSuccess(true);
      
      // Auto sign in after successful registration
      setTimeout(async () => {
        await signIn("credentials", {
          email: data.email,
          password: data.password,
          callbackUrl: callbackUrl || "/dashboard",
        });
      }, 2000);

    } catch (error: any) {
      setError(error.message || "An error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleGoogleSignUp = async () => {
    setIsLoading(true);
    try {
      await signIn("google", { callbackUrl: callbackUrl || "/dashboard" });
    } catch (error) {
      setError("An error occurred. Please try again.");
      setIsLoading(false);
    }
  };

  const formVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: easing.easeOut,
      },
    },
  };

  if (success) {
    return (
      <motion.div
        variants={formVariants}
        initial="hidden"
        animate="visible"
        className="text-center"
      >
        <Card>
          <CardContent className="pt-6">
            <div className="text-green-500 text-6xl mb-4">✓</div>
            <h3 className="text-xl font-semibold mb-2">Account Created Successfully!</h3>
            <p className="text-muted-foreground mb-4">
              Welcome to Boss AI Investment Platform. You&apos;re being signed in...
            </p>
            <div className="animate-pulse text-primary">Redirecting to dashboard...</div>
          </CardContent>
        </Card>
      </motion.div>
    );
  }

  return (
    <motion.div
      variants={formVariants}
      initial="hidden"
      animate="visible"
    >
      <Card>
        <CardHeader>
          <CardTitle>Create Your Account</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Google Sign Up */}
          <AIButton
            type="button"
            variant="outline"
            className="w-full"
            onClick={handleGoogleSignUp}
            disabled={isLoading}
          >
            <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
              <path
                fill="currentColor"
                d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
              />
              <path
                fill="currentColor"
                d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
              />
              <path
                fill="currentColor"
                d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
              />
              <path
                fill="currentColor"
                d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
              />
            </svg>
            Continue with Google
          </AIButton>

          <div className="relative">
            <div className="absolute inset-0 flex items-center">
              <span className="w-full border-t" />
            </div>
            <div className="relative flex justify-center text-xs uppercase">
              <span className="bg-background px-2 text-muted-foreground">Or create with email</span>
            </div>
          </div>

          {/* Error Message */}
          {error && (
            <div className="p-3 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-destructive text-sm">{error}</p>
            </div>
          )}

          {/* Registration Form */}
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label htmlFor="firstName" className="block text-sm font-medium mb-2">
                  First Name
                </label>
                <input
                  {...register("firstName")}
                  type="text"
                  className="w-full p-3 border border-input rounded-lg bg-background"
                  placeholder="John"
                />
                {errors.firstName && (
                  <p className="text-destructive text-sm mt-1">{errors.firstName.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="lastName" className="block text-sm font-medium mb-2">
                  Last Name
                </label>
                <input
                  {...register("lastName")}
                  type="text"
                  className="w-full p-3 border border-input rounded-lg bg-background"
                  placeholder="Doe"
                />
                {errors.lastName && (
                  <p className="text-destructive text-sm mt-1">{errors.lastName.message}</p>
                )}
              </div>
            </div>

            <div>
              <label htmlFor="email" className="block text-sm font-medium mb-2">
                Email Address
              </label>
              <input
                {...register("email")}
                type="email"
                className="w-full p-3 border border-input rounded-lg bg-background"
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="text-destructive text-sm mt-1">{errors.email.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium mb-2">
                Password
              </label>
              <input
                {...register("password")}
                type="password"
                className="w-full p-3 border border-input rounded-lg bg-background"
                placeholder="Create a strong password"
              />
              {errors.password && (
                <p className="text-destructive text-sm mt-1">{errors.password.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="confirmPassword" className="block text-sm font-medium mb-2">
                Confirm Password
              </label>
              <input
                {...register("confirmPassword")}
                type="password"
                className="w-full p-3 border border-input rounded-lg bg-background"
                placeholder="Confirm your password"
              />
              {errors.confirmPassword && (
                <p className="text-destructive text-sm mt-1">{errors.confirmPassword.message}</p>
              )}
            </div>

            {/* Agreements */}
            <div className="space-y-3">
              <div className="flex items-start space-x-2">
                <input
                  {...register("agreeToTerms")}
                  type="checkbox"
                  className="mt-1"
                />
                <label className="text-sm">
                  I agree to the{" "}
                  <a href="/legal/terms" className="text-primary hover:underline" target="_blank">
                    Terms of Service
                  </a>
                </label>
              </div>
              {errors.agreeToTerms && (
                <p className="text-destructive text-sm">{errors.agreeToTerms.message}</p>
              )}

              <div className="flex items-start space-x-2">
                <input
                  {...register("agreeToPrivacy")}
                  type="checkbox"
                  className="mt-1"
                />
                <label className="text-sm">
                  I agree to the{" "}
                  <a href="/legal/privacy" className="text-primary hover:underline" target="_blank">
                    Privacy Policy
                  </a>
                </label>
              </div>
              {errors.agreeToPrivacy && (
                <p className="text-destructive text-sm">{errors.agreeToPrivacy.message}</p>
              )}

              <div className="flex items-start space-x-2">
                <input
                  {...register("agreeToMarketing")}
                  type="checkbox"
                  className="mt-1"
                />
                <label className="text-sm text-muted-foreground">
                  I would like to receive investment opportunities and platform updates (optional)
                </label>
              </div>
            </div>

            <AIButton
              type="submit"
              className="w-full"
              loading={isLoading}
            >
              Create Account
            </AIButton>
          </form>
        </CardContent>
      </Card>
    </motion.div>
  );
}
