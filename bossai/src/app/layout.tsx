import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import { ThemeProvider } from "next-themes";
import { StructuredData } from "@/components/seo/structured-data";
import { AuthProvider } from "@/components/providers/auth-provider";
import "./globals.css";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
  display: "swap",
});

export const metadata: Metadata = {
  title: "Where Vision Meets Intelligence | AI Venture Platform",
  description: "We forge alliances that transform strong products into market leaders through carefully chosen Joint Ventures, bringing intelligence and innovation together.",
  keywords: ["AI", "venture", "partnership", "joint venture", "artificial intelligence", "innovation"],
  authors: [{ name: "AI Venture Platform" }],
  robots: "index, follow",
  openGraph: {
    title: "Where Vision Meets Intelligence",
    description: "Transform your product into a market leader with our AI-powered joint ventures.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "Where Vision Meets Intelligence",
    description: "Transform your product into a market leader with our AI-powered joint ventures.",
  },
};

export const viewport = {
  width: "device-width",
  initialScale: 1,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} font-sans antialiased`}>
        <AuthProvider>
          <ThemeProvider
            attribute="class"
            defaultTheme="dark"
            enableSystem
            disableTransitionOnChange
          >
            <StructuredData />
            {children}
          </ThemeProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
