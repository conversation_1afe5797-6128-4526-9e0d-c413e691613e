import { NextRequest, NextResponse } from 'next/server';
import { InvestmentService } from '@/lib/services/investment.service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '6');

    const featuredProjects = await InvestmentService.getFeaturedProjects(limit);
    
    return NextResponse.json({ projects: featuredProjects });
  } catch (error) {
    console.error('Featured projects API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch featured projects' },
      { status: 500 }
    );
  }
}
