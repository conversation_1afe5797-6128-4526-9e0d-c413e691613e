import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { InvestmentService } from '@/lib/services/investment.service';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '12');
    const sector = searchParams.get('sector') || undefined;
    const riskLevel = searchParams.get('riskLevel') as any || undefined;
    const minInvestment = searchParams.get('minInvestment') ? parseInt(searchParams.get('minInvestment')!) : undefined;
    const maxInvestment = searchParams.get('maxInvestment') ? parseInt(searchParams.get('maxInvestment')!) : undefined;

    const filters = {
      sector,
      riskLevel,
      minInvestment,
      maxInvestment
    };

    const result = await InvestmentService.getAllProjects(page, limit, filters);
    
    return NextResponse.json(result);
  } catch (error) {
    console.error('Projects API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch projects' },
      { status: 500 }
    );
  }
}
