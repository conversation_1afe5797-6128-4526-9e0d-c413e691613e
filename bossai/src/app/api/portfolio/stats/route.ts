import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { PortfolioService } from "@/lib/services/portfolio.service";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get comprehensive portfolio stats using our service
    const [stats, distribution, investments, transactions] = await Promise.all([
      PortfolioService.getPortfolioStats(session.user.id),
      PortfolioService.getPortfolioDistribution(session.user.id),
      PortfolioService.getInvestmentSummary(session.user.id, 5),
      PortfolioService.getRecentTransactions(session.user.id, 5)
    ]);

    // Calculate portfolio distribution percentages
    const totalDistributionValue = distribution.reduce((sum, item) => sum + item.value, 0);
    const assetAllocation = distribution.map(item => ({
      category: item.sector,
      percentage: totalDistributionValue > 0 ? (item.value / totalDistributionValue) * 100 : 0,
      amount: item.value,
      count: item.count
    }));

    // Generate mock historical data for charts (in production, store historical snapshots)
    const portfolioGrowthData = Array.from({ length: 12 }, (_, i) => {
      const date = new Date();
      date.setMonth(date.getMonth() - (11 - i));
      const growthFactor = 1 + (stats.yearlyGrowth / 100) * (i / 11);
      return {
        date: date.toISOString().slice(0, 7), // YYYY-MM format
        value: Math.round(stats.totalValue * growthFactor)
      };
    });

    const response = {
      ...stats,
      totalInvested: stats.totalValue - stats.totalReturns, // Calculate invested amount
      portfolioGrowth: stats.totalValue > 0 ? (stats.totalReturns / (stats.totalValue - stats.totalReturns)) * 100 : 0,
      portfolioGrowthData,
      assetAllocation,
      recentInvestments: investments,
      recentTransactions: transactions,
      performanceMetrics: {
        roi: stats.averageReturn,
        volatility: 8.5, // Would be calculated from historical data
        sharpeRatio: 1.8, // Would be calculated from risk-free rate and volatility
      },
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching portfolio stats:", error);
    return NextResponse.json(
      { error: "Failed to fetch portfolio statistics" },
      { status: 500 }
    );
  }
}
