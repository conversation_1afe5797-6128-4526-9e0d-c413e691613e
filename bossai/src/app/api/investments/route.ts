import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { z } from "zod";

// Investment submission schema
const investmentSchema = z.object({
  type: z.enum(["EQUITY", "DEBT", "HYBRID", "REAL_ESTATE", "CRYPTOCURRENCY", "COMMODITIES", "OTHER"]),
  amount: z.number().min(1000).max(10000000),
  currency: z.string().default("USD"),
  riskLevel: z.enum(["CONSERVATIVE", "MODERATE", "AGGRESSIVE", "VERY_AGGRESSIVE"]),
  duration: z.number().min(1).max(120).optional(),
  description: z.string().min(10).max(1000),
  expectedReturn: z.number().min(0).max(100).optional(),
  
  // Personal Information
  firstName: z.string().min(2),
  lastName: z.string().min(2),
  phone: z.string().min(10),
  dateOfBirth: z.string(),
  address: z.string().min(10),
  city: z.string().min(2),
  state: z.string().min(2),
  zipCode: z.string().min(5),
  country: z.string().min(2),
  
  // Investment Experience
  investmentExperience: z.enum(["BEGINNER", "INTERMEDIATE", "ADVANCED", "PROFESSIONAL"]),
  annualIncome: z.enum(["UNDER_50K", "50K_100K", "100K_250K", "250K_500K", "500K_1M", "OVER_1M"]),
  netWorth: z.enum(["UNDER_100K", "100K_500K", "500K_1M", "1M_5M", "5M_10M", "OVER_10M"]),
  
  // Legal
  accreditedInvestor: z.boolean(),
  agreeToTerms: z.boolean(),
  agreeToPrivacy: z.boolean(),
});

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check user role
    if (session.user.role !== "INVESTOR" && session.user.role !== "ADMIN") {
      return NextResponse.json({ error: "Insufficient permissions" }, { status: 403 });
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = investmentSchema.parse(body);

    // Update user profile with KYC information
    await prisma.user.update({
      where: { id: session.user.id },
      data: {
        firstName: validatedData.firstName,
        lastName: validatedData.lastName,
        phone: validatedData.phone,
        dateOfBirth: new Date(validatedData.dateOfBirth),
        address: validatedData.address,
        city: validatedData.city,
        state: validatedData.state,
        zipCode: validatedData.zipCode,
        country: validatedData.country,
        riskTolerance: validatedData.riskLevel,
        investmentExperience: validatedData.investmentExperience,
        annualIncome: validatedData.annualIncome,
        netWorth: validatedData.netWorth,
        kycStatus: "PENDING_REVIEW",
      },
    });

    // Create investment record
    const investment = await prisma.investment.create({
      data: {
        userId: session.user.id,
        type: validatedData.type,
        amount: validatedData.amount,
        currency: validatedData.currency,
        riskLevel: validatedData.riskLevel,
        duration: validatedData.duration,
        description: validatedData.description,
        expectedReturn: validatedData.expectedReturn,
        status: "PENDING",
      },
    });

    // Create audit log
    await prisma.auditLog.create({
      data: {
        userId: session.user.id,
        action: "INVESTMENT_SUBMITTED",
        entity: "Investment",
        entityId: investment.id,
        newValues: {
          type: validatedData.type,
          amount: validatedData.amount,
          riskLevel: validatedData.riskLevel,
        },
      },
    });

    return NextResponse.json({
      success: true,
      investmentId: investment.id,
      message: "Investment application submitted successfully",
    });

  } catch (error) {
    console.error("Investment submission error:", error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: "Invalid data", details: error.issues },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    if (!session) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's investments
    const investments = await prisma.investment.findMany({
      where: { userId: session.user.id },
      orderBy: { createdAt: "desc" },
      include: {
        documents: true,
        transactions: true,
      },
    });

    return NextResponse.json({ investments });

  } catch (error) {
    console.error("Error fetching investments:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
