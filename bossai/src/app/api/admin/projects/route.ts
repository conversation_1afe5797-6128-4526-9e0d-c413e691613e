import { NextRequest, NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "@/lib/auth";
import { prisma } from "@/lib/prisma";
import { ProjectStatus, RiskLevel } from "@prisma/client";

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    });

    if (!user || (user.role !== "ADMIN" && user.role !== "SUPER_ADMIN")) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Fetch all projects with investment counts
    const projects = await prisma.project.findMany({
      include: {
        investments: {
          select: {
            id: true,
            amount: true,
            status: true
          }
        },
        creator: {
          select: {
            name: true,
            email: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Transform projects with calculated fields
    const transformedProjects = projects.map(project => ({
      id: project.id,
      title: project.title,
      description: project.description,
      sector: project.sector,
      fundingGoal: Number(project.fundingGoal),
      currentFunding: Number(project.currentFunding),
      roiEstimate: Number(project.roiEstimate),
      status: project.status,
      investorCount: project.investments.length,
      startDate: project.startDate?.toISOString().split('T')[0] || '',
      endDate: project.endDate?.toISOString().split('T')[0] || '',
      riskLevel: project.riskLevel,
      featured: project.featured,
      minInvestment: Number(project.minInvestment),
      maxInvestment: Number(project.maxInvestment),
      timeHorizon: project.timeHorizon,
      imageUrl: project.imageUrl,
      tags: project.tags,
      createdAt: project.createdAt,
      updatedAt: project.updatedAt,
      creator: project.creator
    }));

    return NextResponse.json({ 
      projects: transformedProjects,
      total: transformedProjects.length 
    });
  } catch (error) {
    console.error("Error fetching admin projects:", error);
    return NextResponse.json(
      { error: "Failed to fetch projects" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is admin
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { role: true }
    });

    if (!user || (user.role !== "ADMIN" && user.role !== "SUPER_ADMIN")) {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    const body = await request.json();
    const {
      title,
      description,
      sector,
      fundingGoal,
      roiEstimate,
      status,
      featured,
      minInvestment,
      maxInvestment,
      riskLevel,
      timeHorizon,
      startDate,
      endDate,
      imageUrl,
      tags
    } = body;

    // Validate required fields
    if (!title || !description || !sector || !fundingGoal || !roiEstimate) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      );
    }

    // Create new project
    const project = await prisma.project.create({
      data: {
        title,
        description,
        sector,
        fundingGoal: Number(fundingGoal),
        currentFunding: 0,
        roiEstimate: Number(roiEstimate),
        status: status as ProjectStatus || 'DRAFT',
        featured: Boolean(featured),
        minInvestment: Number(minInvestment) || 1000,
        maxInvestment: Number(maxInvestment) || Number(fundingGoal),
        riskLevel: riskLevel as RiskLevel || 'MODERATE',
        timeHorizon: timeHorizon || '1-3 years',
        startDate: startDate ? new Date(startDate) : new Date(),
        endDate: endDate ? new Date(endDate) : null,
        imageUrl: imageUrl || null,
        tags: tags || '',
        creatorId: session.user.id
      },
      include: {
        creator: {
          select: {
            name: true,
            email: true
          }
        }
      }
    });

    return NextResponse.json({ 
      project: {
        ...project,
        fundingGoal: Number(project.fundingGoal),
        currentFunding: Number(project.currentFunding),
        roiEstimate: Number(project.roiEstimate),
        minInvestment: Number(project.minInvestment),
        maxInvestment: Number(project.maxInvestment)
      }
    }, { status: 201 });
  } catch (error) {
    console.error("Error creating project:", error);
    return NextResponse.json(
      { error: "Failed to create project" },
      { status: 500 }
    );
  }
}
