import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { InvestmentService } from '@/lib/services/investment.service';
import { UserService } from '@/lib/services/user.service';

export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user?.id) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get comprehensive dashboard metrics
    const [investmentMetrics, userStats, sectorDistribution] = await Promise.all([
      InvestmentService.getDashboardMetrics(),
      UserService.getUserStats(),
      InvestmentService.getSectorDistribution()
    ]);

    const metrics = {
      ...investmentMetrics,
      ...userStats,
      sectorDistribution
    };
    
    return NextResponse.json(metrics);
  } catch (error) {
    console.error('Dashboard metrics API error:', error);
    return NextResponse.json(
      { error: 'Failed to fetch dashboard metrics' },
      { status: 500 }
    );
  }
}
