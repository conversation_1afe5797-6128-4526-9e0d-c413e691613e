import { Metadata } from "next";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";
import { InvestmentForm } from "@/components/investment/investment-form";
import { Container } from "@/components/ui/container";
import { Section } from "@/components/ui/section";

export const metadata: Metadata = {
  title: "Boss AI Investment Form | Secure Investment Portal",
  description: "Submit your investment application through our secure, encrypted platform. Access restricted to verified investors only.",
  robots: "noindex, nofollow", // Private page
};

export default async function BossAIInvestmentsPage() {
  const session = await getServerSession(authOptions);

  // Redirect if not authenticated
  if (!session) {
    redirect("/auth/signin?callbackUrl=/boss-ai-investments");
  }

  // Check if user has investor role
  if (session.user.role !== "INVESTOR" && session.user.role !== "ADMIN") {
    redirect("/dashboard?error=insufficient-permissions");
  }

  return (
    <Section background="default" className="min-h-screen py-20">
      <Container size="lg">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-black mb-6 gradient-text">
              Boss AI Investment Portal
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Submit your investment application through our secure, encrypted platform. 
              All information is protected with enterprise-grade security.
            </p>
          </div>

          {/* Investment Form */}
          <InvestmentForm />

          {/* Security Notice */}
          <div className="mt-12 p-6 bg-muted/50 rounded-lg border">
            <h3 className="text-lg font-semibold mb-3 flex items-center gap-2">
              <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              Security & Privacy
            </h3>
            <ul className="text-sm text-muted-foreground space-y-2">
              <li>• All data is encrypted in transit and at rest using AES-256 encryption</li>
              <li>• Documents are stored in secure, compliant cloud infrastructure</li>
              <li>• Your information is only accessible to authorized personnel</li>
              <li>• We comply with SOC 2 Type II and industry security standards</li>
            </ul>
          </div>
        </div>
      </Container>
    </Section>
  );
}
