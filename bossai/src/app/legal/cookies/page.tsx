import { Metadata } from "next";
import { Container } from "@/components/ui/container";
import { Section } from "@/components/ui/section";

export const metadata: Metadata = {
  title: "Cookie Policy | Boss AI Investment Platform",
  description: "Cookie Policy for Boss AI Investment Platform - How we use cookies and tracking technologies.",
};

export default function CookiesPage() {
  return (
    <Section background="default" className="min-h-screen py-20">
      <Container size="lg">
        <div className="max-w-4xl mx-auto">
          <div className="mb-12">
            <h1 className="text-4xl md:text-5xl font-black mb-4 gradient-text">
              Cookie Policy
            </h1>
            <p className="text-xl text-muted-foreground">
              Last updated: {new Date().toLocaleDateString()}
            </p>
          </div>

          <div className="prose prose-lg max-w-none">
            <h2>1. What Are Cookies</h2>
            <p>
              Cookies are small text files that are stored on your device when you visit our website. They help us provide you with a better experience by remembering your preferences and analyzing how you use our platform.
            </p>

            <h2>2. Types of Cookies We Use</h2>
            
            <h3>2.1 Essential Cookies</h3>
            <p>These cookies are necessary for the website to function properly:</p>
            <ul>
              <li>Authentication cookies to keep you logged in</li>
              <li>Security cookies to protect against fraud</li>
              <li>Session cookies to maintain your browsing session</li>
              <li>Load balancing cookies for optimal performance</li>
            </ul>

            <h3>2.2 Analytics Cookies</h3>
            <p>These cookies help us understand how visitors interact with our website:</p>
            <ul>
              <li>Google Analytics for website usage statistics</li>
              <li>Performance monitoring cookies</li>
              <li>Error tracking cookies</li>
              <li>User behavior analysis cookies</li>
            </ul>

            <h3>2.3 Functional Cookies</h3>
            <p>These cookies enhance your experience on our platform:</p>
            <ul>
              <li>Language preference cookies</li>
              <li>Theme and display preference cookies</li>
              <li>Form data cookies to save your progress</li>
              <li>Accessibility feature cookies</li>
            </ul>

            <h3>2.4 Marketing Cookies</h3>
            <p>These cookies are used for advertising and marketing purposes:</p>
            <ul>
              <li>Advertising network cookies</li>
              <li>Social media integration cookies</li>
              <li>Remarketing and retargeting cookies</li>
              <li>Campaign tracking cookies</li>
            </ul>

            <h2>3. Third-Party Cookies</h2>
            <p>We may use third-party services that set their own cookies:</p>
            <ul>
              <li><strong>Google Analytics:</strong> For website analytics and performance monitoring</li>
              <li><strong>Google OAuth:</strong> For secure authentication</li>
              <li><strong>Stripe:</strong> For payment processing (when applicable)</li>
              <li><strong>AWS CloudFront:</strong> For content delivery and performance</li>
            </ul>

            <h2>4. Cookie Duration</h2>
            
            <h3>4.1 Session Cookies</h3>
            <p>These cookies are temporary and are deleted when you close your browser.</p>

            <h3>4.2 Persistent Cookies</h3>
            <p>These cookies remain on your device for a set period or until you delete them:</p>
            <ul>
              <li>Authentication cookies: 30 days</li>
              <li>Preference cookies: 1 year</li>
              <li>Analytics cookies: 2 years</li>
              <li>Marketing cookies: 90 days</li>
            </ul>

            <h2>5. Managing Cookies</h2>
            
            <h3>5.1 Browser Settings</h3>
            <p>You can control cookies through your browser settings:</p>
            <ul>
              <li>Block all cookies</li>
              <li>Block third-party cookies only</li>
              <li>Delete existing cookies</li>
              <li>Receive notifications when cookies are set</li>
            </ul>

            <h3>5.2 Cookie Consent</h3>
            <p>
              When you first visit our website, we will ask for your consent to use non-essential cookies. You can change your preferences at any time through our cookie consent banner or by contacting us.
            </p>

            <h2>6. Impact of Disabling Cookies</h2>
            <p>
              If you disable cookies, some features of our platform may not work properly:
            </p>
            <ul>
              <li>You may need to log in repeatedly</li>
              <li>Your preferences may not be saved</li>
              <li>Some interactive features may not function</li>
              <li>We may not be able to provide personalized content</li>
            </ul>

            <h2>7. Updates to This Policy</h2>
            <p>
              We may update this Cookie Policy from time to time to reflect changes in our practices or for other operational, legal, or regulatory reasons.
            </p>

            <h2>8. Contact Us</h2>
            <p>
              If you have any questions about our use of cookies, please contact us at:
            </p>
            <ul>
              <li>Email: <EMAIL></li>
              <li>Subject: Cookie Policy Inquiry</li>
              <li>Address: [Company Address]</li>
            </ul>

            <div className="mt-8 p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h3 className="text-blue-800 font-semibold mb-2">Cookie Consent</h3>
              <p className="text-blue-700 text-sm">
                By continuing to use our website, you consent to our use of cookies as described in this policy. 
                You can withdraw your consent at any time by adjusting your browser settings or contacting us.
              </p>
            </div>
          </div>
        </div>
      </Container>
    </Section>
  );
}
