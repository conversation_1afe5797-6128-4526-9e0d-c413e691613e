import { Metada<PERSON> } from "next";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";
import { prisma } from "@/lib/prisma";
import { AdminDashboard } from "@/components/admin/admin-dashboard";
import { Container } from "@/components/ui/container";
import { Section } from "@/components/ui/section";

export const metadata: Metadata = {
  title: "Admin Dashboard | Boss AI Investment Platform",
  description: "Administrative dashboard for managing users, investments, and platform operations.",
  robots: "noindex, nofollow", // Private page
};

export default async function AdminPage() {
  const session = await getServerSession(authOptions);

  // Redirect if not authenticated
  if (!session) {
    redirect("/auth/signin?callbackUrl=/admin");
  }

  // Check admin permissions
  if (session.user.role !== "ADMIN" && session.user.role !== "SUPER_ADMIN") {
    redirect("/dashboard?error=insufficient-permissions");
  }

  // Fetch admin dashboard data
  const [users, investments, documents, auditLogs] = await Promise.all([
    prisma.user.findMany({
      orderBy: { createdAt: "desc" },
      take: 50,
      include: {
        investments: true,
        documents: true,
      },
    }),
    prisma.investment.findMany({
      orderBy: { createdAt: "desc" },
      take: 50,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
            firstName: true,
            lastName: true,
          },
        },
        documents: true,
      },
    }),
    prisma.document.findMany({
      orderBy: { uploadedAt: "desc" },
      take: 50,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    }),
    prisma.auditLog.findMany({
      orderBy: { createdAt: "desc" },
      take: 100,
      include: {
        user: {
          select: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    }),
  ]);

  // Calculate stats
  const stats = {
    totalUsers: await prisma.user.count(),
    totalInvestments: await prisma.investment.count(),
    pendingInvestments: await prisma.investment.count({
      where: { status: "PENDING" },
    }),
    totalInvestmentValue: await prisma.investment.aggregate({
      _sum: { amount: true },
    }),
    pendingKYC: await prisma.user.count({
      where: { kycStatus: "PENDING_REVIEW" },
    }),
  };

  return (
    <Section background="default" className="min-h-screen py-20">
      <Container size="xl">
        <div className="max-w-7xl mx-auto">
          {/* Header */}
          <div className="mb-12">
            <h1 className="text-4xl md:text-5xl font-black mb-4 gradient-text">
              Admin Dashboard
            </h1>
            <p className="text-xl text-muted-foreground">
              Manage users, investments, and platform operations
            </p>
          </div>

          {/* Admin Dashboard Content */}
          <AdminDashboard
            users={users}
            investments={investments}
            documents={documents}
            auditLogs={auditLogs}
            stats={stats}
            currentUser={session.user}
          />
        </div>
      </Container>
    </Section>
  );
}
