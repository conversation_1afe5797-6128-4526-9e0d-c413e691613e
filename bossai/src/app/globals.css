@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  /* AI-Inspired Color Palette */
  --color-deep-space: #0f172a;
  --color-electric-blue: #3b82f6;
  --color-quantum-purple: #8b5cf6;
  --color-cyber-green: #22c55e;
  --color-charcoal-black: #0a0a0a;
  --color-cloud-gray: #9ca3af;

  /* Core System Colors */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter), var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Component Colors */
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);

  /* Border Radius */
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;

  /* Light theme - AI-inspired colors with WCAG AA compliance */
  --background: #ffffff;
  --foreground: #111827;
  --card: #ffffff;
  --card-foreground: #111827;
  --popover: #ffffff;
  --popover-foreground: #111827;
  --primary: #2563eb;
  --primary-foreground: #ffffff;
  --secondary: #f3f4f6;
  --secondary-foreground: #111827;
  --muted: #f9fafb;
  --muted-foreground: #374151;
  --accent: #8b5cf6;
  --accent-foreground: #ffffff;
  --destructive: #dc2626;
  --border: #d1d5db;
  --input: #d1d5db;
  --ring: #2563eb;

  /* Chart colors - AI theme */
  --chart-1: #3b82f6;
  --chart-2: #8b5cf6;
  --chart-3: #22c55e;
  --chart-4: #f59e0b;
  --chart-5: #ef4444;

  /* Sidebar */
  --sidebar: #ffffff;
  --sidebar-foreground: #0f172a;
  --sidebar-primary: #3b82f6;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #f1f5f9;
  --sidebar-accent-foreground: #0f172a;
  --sidebar-border: #e2e8f0;
  --sidebar-ring: #3b82f6;
}

.dark {
  /* Dark theme - Deep space AI aesthetic */
  --background: #0f172a;
  --foreground: #f8fafc;
  --card: #1e293b;
  --card-foreground: #f8fafc;
  --popover: #1e293b;
  --popover-foreground: #f8fafc;
  --primary: #3b82f6;
  --primary-foreground: #ffffff;
  --secondary: #334155;
  --secondary-foreground: #f8fafc;
  --muted: #1e293b;
  --muted-foreground: #94a3b8;
  --accent: #8b5cf6;
  --accent-foreground: #ffffff;
  --destructive: #ef4444;
  --border: #334155;
  --input: #334155;
  --ring: #3b82f6;

  /* Chart colors - enhanced for dark theme */
  --chart-1: #60a5fa;
  --chart-2: #a78bfa;
  --chart-3: #4ade80;
  --chart-4: #fbbf24;
  --chart-5: #f87171;

  /* Sidebar dark theme */
  --sidebar: #1e293b;
  --sidebar-foreground: #f8fafc;
  --sidebar-primary: #3b82f6;
  --sidebar-primary-foreground: #ffffff;
  --sidebar-accent: #334155;
  --sidebar-accent-foreground: #f8fafc;
  --sidebar-border: #334155;
  --sidebar-ring: #3b82f6;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground font-sans;
  }
}

@layer components {
  /* AI-Inspired Gradient Backgrounds */
  .gradient-bg {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #334155 100%);
  }

  .ai-pattern {
    background-image:
      radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.1) 0%, transparent 50%),
      radial-gradient(circle at 50% 50%, rgba(34, 197, 94, 0.05) 0%, transparent 50%);
  }

  /* Text Effects */
  .glow-text {
    text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
  }

  .gradient-text {
    @apply bg-gradient-to-r from-blue-400 via-purple-500 to-green-400 bg-clip-text text-transparent;
  }

  /* Tech Grid Pattern */
  .tech-grid {
    background-image:
      linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
      linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
    background-size: 50px 50px;
  }

  /* Neural Network Pattern */
  .neural-network {
    position: absolute;
    width: 100%;
    height: 100%;
    opacity: 0.1;
    background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3Ccircle cx='10' cy='10' r='1'/%3E%3Ccircle cx='50' cy='10' r='1'/%3E%3Ccircle cx='10' cy='50' r='1'/%3E%3Ccircle cx='50' cy='50' r='1'/%3E%3Cline x1='30' y1='30' x2='10' y2='10' stroke='%233b82f6' stroke-width='0.5'/%3E%3Cline x1='30' y1='30' x2='50' y2='10' stroke='%233b82f6' stroke-width='0.5'/%3E%3Cline x1='30' y1='30' x2='10' y2='50' stroke='%233b82f6' stroke-width='0.5'/%3E%3Cline x1='30' y1='30' x2='50' y2='50' stroke='%233b82f6' stroke-width='0.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
  }

  /* Interactive Card Effects */
  .card-hover {
    @apply transition-all duration-300 ease-in-out;
  }

  .card-hover:hover {
    @apply -translate-y-2;
    box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
  }

  /* Button Styles */
  .btn-primary {
    @apply bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 transition-all duration-300 transform hover:-translate-y-1;
    box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
  }

  .btn-primary:hover {
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-blue-500/10 to-purple-600/10 border border-blue-500/30 hover:border-blue-500/50 transition-all duration-300 transform hover:-translate-y-1;
  }

  /* Floating Orb Animations */
  .floating-orb {
    @apply absolute rounded-full blur-sm;
    animation: float 6s ease-in-out infinite;
  }

  .floating-orb:nth-child(1) {
    @apply w-24 h-24;
    background: linear-gradient(45deg, rgba(59, 130, 246, 0.3), rgba(139, 92, 246, 0.3));
    top: 20%;
    left: 10%;
    animation-delay: 0s;
  }

  .floating-orb:nth-child(2) {
    @apply w-16 h-16;
    background: linear-gradient(45deg, rgba(34, 197, 94, 0.3), rgba(59, 130, 246, 0.3));
    top: 60%;
    right: 15%;
    animation-delay: 2s;
  }

  .floating-orb:nth-child(3) {
    @apply w-20 h-20;
    background: linear-gradient(45deg, rgba(139, 92, 246, 0.3), rgba(34, 197, 94, 0.3));
    bottom: 30%;
    left: 20%;
    animation-delay: 4s;
  }
}

@layer utilities {
  /* Custom Animations */
  @keyframes float {
    0%, 100% {
      transform: translateY(0px) rotate(0deg);
    }
    33% {
      transform: translateY(-20px) rotate(120deg);
    }
    66% {
      transform: translateY(10px) rotate(240deg);
    }
  }

  /* Responsive Typography */
  .text-display {
    @apply text-6xl md:text-8xl font-black leading-tight;
  }

  .text-hero {
    @apply text-4xl md:text-6xl font-bold leading-tight;
  }

  .text-section {
    @apply text-3xl md:text-5xl font-bold;
  }
}
