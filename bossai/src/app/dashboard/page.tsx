"use client";

import { useSession } from "next-auth/react";
import { redirect } from "next/navigation";
import { DashboardProvider } from "@/contexts/DashboardContext";
import { DashboardLayout } from "@/components/dashboard/DashboardLayout";
import { LoadingSpinner } from "@/components/ui/loading-spinner";

export default function DashboardPage() {
  const { data: session, status } = useSession();

  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-900 flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (status === "unauthenticated") {
    redirect("/auth/signin");
  }

  return (
    <DashboardProvider>
      <DashboardLayout />
    </DashboardProvider>
  );
}