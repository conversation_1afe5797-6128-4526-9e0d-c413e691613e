import { Metadata } from "next";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";
import { SignUpForm } from "@/components/auth/signup-form";
import { Container } from "@/components/ui/container";
import { Section } from "@/components/ui/section";

export const metadata: Metadata = {
  title: "Sign Up | Boss AI Investment Platform",
  description: "Create your secure investment account. Join the future of AI-powered venture investments.",
};

interface SignUpPageProps {
  searchParams: Promise<{
    callbackUrl?: string;
    error?: string;
  }>;
}

export default async function SignUpPage({ searchParams }: SignUpPageProps) {
  const params = await searchParams;
  const session = await getServerSession(authOptions);

  // Redirect if already authenticated
  if (session) {
    redirect(params.callbackUrl || "/dashboard");
  }

  return (
    <Section background="default" className="min-h-screen py-20">
      <Container size="md">
        <div className="max-w-md mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-black mb-4 gradient-text">
              Join Boss AI
            </h1>
            <p className="text-muted-foreground">
              Create your account and start investing in the future
            </p>
          </div>

          {/* Error Message */}
          {params.error && (
            <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-destructive text-sm">
                {params.error === "OAuthAccountNotLinked" 
                  ? "An account with this email already exists. Please sign in instead."
                  : "An error occurred during sign up. Please try again."
                }
              </p>
            </div>
          )}

          {/* Sign Up Form */}
          <SignUpForm callbackUrl={params.callbackUrl} />

          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-sm text-muted-foreground">
              Already have an account?{" "}
              <a href="/auth/signin" className="text-primary hover:underline">
                Sign in here
              </a>
            </p>
          </div>

          {/* Legal Notice */}
          <div className="mt-6 p-4 bg-muted/50 rounded-lg">
            <p className="text-xs text-muted-foreground text-center">
              By creating an account, you agree to our{" "}
              <a href="/legal/terms" className="text-primary hover:underline">Terms of Service</a>,{" "}
              <a href="/legal/privacy" className="text-primary hover:underline">Privacy Policy</a>, and{" "}
              <a href="/legal/cookies" className="text-primary hover:underline">Cookie Policy</a>.
              Investment opportunities are subject to regulatory compliance and KYC/AML verification.
            </p>
          </div>
        </div>
      </Container>
    </Section>
  );
}
