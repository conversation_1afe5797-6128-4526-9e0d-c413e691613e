import { Metadata } from "next";
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import { redirect } from "next/navigation";
import { SignInForm } from "@/components/auth/signin-form";
import { Container } from "@/components/ui/container";
import { Section } from "@/components/ui/section";

export const metadata: Metadata = {
  title: "Sign In | Boss AI Investment Platform",
  description: "Access your secure investment portal. Sign in to manage your portfolio and investment applications.",
};

interface SignInPageProps {
  searchParams: Promise<{
    callbackUrl?: string;
    error?: string;
  }>;
}

export default async function SignInPage({ searchParams }: SignInPageProps) {
  const params = await searchParams;
  const session = await getServerSession(authOptions);

  // Redirect if already authenticated
  if (session) {
    redirect(params.callbackUrl || "/dashboard");
  }

  return (
    <Section background="default" className="min-h-screen py-20">
      <Container size="md">
        <div className="max-w-md mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <h1 className="text-3xl md:text-4xl font-black mb-4 gradient-text">
              Welcome Back
            </h1>
            <p className="text-muted-foreground">
              Sign in to access your investment portal
            </p>
          </div>

          {/* Error Message */}
          {params.error && (
            <div className="mb-6 p-4 bg-destructive/10 border border-destructive/20 rounded-lg">
              <p className="text-destructive text-sm">
                {params.error === "CredentialsSignin"
                  ? "Invalid email or password. Please try again."
                  : "An error occurred during sign in. Please try again."
                }
              </p>
            </div>
          )}

          {/* Sign In Form */}
          <SignInForm callbackUrl={params.callbackUrl} />

          {/* Footer */}
          <div className="mt-8 text-center">
            <p className="text-sm text-muted-foreground">
              Don&apos;t have an account?{" "}
              <a href="/auth/signup" className="text-primary hover:underline">
                Sign up here
              </a>
            </p>
          </div>
        </div>
      </Container>
    </Section>
  );
}
