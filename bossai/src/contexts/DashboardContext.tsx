"use client";

import React, { createContext, useContext, useReducer, useEffect } from 'react';
import { useSession } from 'next-auth/react';

// Types
// Admin Dashboard Views
export type AdminActiveView =
  | "admin-dashboard"
  | "admin-projects"
  | "admin-investors"
  | "admin-form-submissions"
  | "admin-analytics"
  | "admin-documents"
  | "admin-notifications"
  | "admin-settings"
  | "admin-support";

// Investor Dashboard Views
export type InvestorActiveView =
  | "investor-dashboard"
  | "investor-projects"
  | "investor-boss-ai-form"
  | "investor-analytics"
  | "investor-documents"
  | "investor-notifications"
  | "investor-settings"
  | "investor-support";

export type ActiveView = AdminActiveView | InvestorActiveView;

export interface User {
  id: string;
  name?: string;
  email: string;
  role: 'USER' | 'INVESTOR' | 'ADMIN' | 'SUPER_ADMIN';
  kycStatus: 'NOT_STARTED' | 'IN_PROGRESS' | 'PENDING_REVIEW' | 'APPROVED' | 'REJECTED' | 'EXPIRED';
  firstName?: string;
  lastName?: string;
  image?: string;
}

export interface Investment {
  id: string;
  type: string;
  amount: number;
  status: 'PENDING' | 'UNDER_REVIEW' | 'APPROVED' | 'REJECTED' | 'ACTIVE' | 'COMPLETED' | 'CANCELLED';
  description?: string;
  expectedReturn?: number;
  currentValue?: number;
  totalReturns?: number;
  riskLevel: 'CONSERVATIVE' | 'MODERATE' | 'AGGRESSIVE' | 'VERY_AGGRESSIVE';
  submittedAt: string;
  approvedAt?: string;
}

export interface PortfolioStats {
  totalInvested: number;
  totalValue: number;
  totalReturns: number;
  activeInvestments: number;
  pendingInvestments: number;
  completedInvestments: number;
  portfolioGrowth: number;
}

export interface Notification {
  id: string;
  type: 'success' | 'warning' | 'info' | 'error';
  title: string;
  message: string;
  actionUrl?: string;
  isRead: boolean;
  createdAt: string;
}

export interface DashboardState {
  activeView: ActiveView;
  user: User | null;
  investments: Investment[];
  portfolioStats: PortfolioStats | null;
  notifications: Notification[];
  isLoading: boolean;
  error: string | null;
  sidebarOpen: boolean;
}

// Actions
type DashboardAction =
  | { type: 'SET_ACTIVE_VIEW'; payload: ActiveView }
  | { type: 'SET_USER'; payload: User | null }
  | { type: 'SET_INVESTMENTS'; payload: Investment[] }
  | { type: 'SET_PORTFOLIO_STATS'; payload: PortfolioStats | null }
  | { type: 'SET_NOTIFICATIONS'; payload: Notification[] }
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'TOGGLE_SIDEBAR' }
  | { type: 'SET_SIDEBAR_OPEN'; payload: boolean }
  | { type: 'ADD_INVESTMENT'; payload: Investment }
  | { type: 'UPDATE_INVESTMENT'; payload: { id: string; updates: Partial<Investment> } }
  | { type: 'MARK_NOTIFICATION_READ'; payload: string };

// Initial state
const initialState: DashboardState = {
  activeView: 'investor-dashboard', // Default to investor view
  user: null,
  investments: [],
  portfolioStats: null,
  notifications: [],
  isLoading: false,
  error: null,
  sidebarOpen: true, // Default to open on desktop
};

// Reducer
function dashboardReducer(state: DashboardState, action: DashboardAction): DashboardState {
  switch (action.type) {
    case 'SET_ACTIVE_VIEW':
      return { ...state, activeView: action.payload };
    
    case 'SET_USER':
      return { ...state, user: action.payload };
    
    case 'SET_INVESTMENTS':
      return { ...state, investments: action.payload };
    
    case 'SET_PORTFOLIO_STATS':
      return { ...state, portfolioStats: action.payload };
    
    case 'SET_NOTIFICATIONS':
      return { ...state, notifications: action.payload };
    
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload };
    
    case 'TOGGLE_SIDEBAR':
      return { ...state, sidebarOpen: !state.sidebarOpen };
    
    case 'SET_SIDEBAR_OPEN':
      return { ...state, sidebarOpen: action.payload };
    
    case 'ADD_INVESTMENT':
      return { 
        ...state, 
        investments: [...state.investments, action.payload] 
      };
    
    case 'UPDATE_INVESTMENT':
      return {
        ...state,
        investments: state.investments.map(inv =>
          inv.id === action.payload.id 
            ? { ...inv, ...action.payload.updates }
            : inv
        )
      };
    
    case 'MARK_NOTIFICATION_READ':
      return {
        ...state,
        notifications: state.notifications.map(notif =>
          notif.id === action.payload 
            ? { ...notif, isRead: true }
            : notif
        )
      };
    
    default:
      return state;
  }
}

// Context
interface DashboardContextType {
  state: DashboardState;
  dispatch: React.Dispatch<DashboardAction>;
  // Helper functions
  setActiveView: (view: ActiveView) => void;
  toggleSidebar: () => void;
  setSidebarOpen: (open: boolean) => void;
  markNotificationRead: (id: string) => void;
  refreshData: () => Promise<void>;
}

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

// Provider
export function DashboardProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(dashboardReducer, initialState);
  const { data: session, status } = useSession();

  // Helper functions
  const setActiveView = (view: ActiveView) => {
    dispatch({ type: 'SET_ACTIVE_VIEW', payload: view });
  };

  const toggleSidebar = () => {
    dispatch({ type: 'TOGGLE_SIDEBAR' });
  };

  const setSidebarOpen = (open: boolean) => {
    dispatch({ type: 'SET_SIDEBAR_OPEN', payload: open });
  };

  const markNotificationRead = (id: string) => {
    dispatch({ type: 'MARK_NOTIFICATION_READ', payload: id });
  };

  const refreshData = async () => {
    if (!session?.user?.email) return;

    dispatch({ type: 'SET_LOADING', payload: true });
    dispatch({ type: 'SET_ERROR', payload: null });

    try {
      // Fetch user data
      const userResponse = await fetch('/api/user/profile');
      if (userResponse.ok) {
        const userData = await userResponse.json();
        dispatch({ type: 'SET_USER', payload: userData });
      }

      // Fetch investments
      const investmentsResponse = await fetch('/api/investments');
      if (investmentsResponse.ok) {
        const investmentsData = await investmentsResponse.json();
        dispatch({ type: 'SET_INVESTMENTS', payload: investmentsData });
      }

      // Fetch portfolio stats
      const statsResponse = await fetch('/api/portfolio/stats');
      if (statsResponse.ok) {
        const statsData = await statsResponse.json();
        dispatch({ type: 'SET_PORTFOLIO_STATS', payload: statsData });
      }

      // Fetch notifications
      const notificationsResponse = await fetch('/api/notifications');
      if (notificationsResponse.ok) {
        const notificationsData = await notificationsResponse.json();
        // Ensure we always set an array
        const notifications = Array.isArray(notificationsData) ? notificationsData : [];
        dispatch({ type: 'SET_NOTIFICATIONS', payload: notifications });
      }

    } catch (error) {
      console.error('Error fetching dashboard data:', error);
      dispatch({ type: 'SET_ERROR', payload: 'Failed to load dashboard data' });
    } finally {
      dispatch({ type: 'SET_LOADING', payload: false });
    }
  };

  // Initialize data when session is available and set default view based on role
  useEffect(() => {
    if (status === 'authenticated' && session?.user) {
      // Set default view based on user role
      const userRole = session.user.role;
      const isAdmin = userRole === "ADMIN" || userRole === "SUPER_ADMIN";
      const defaultView: ActiveView = isAdmin ? "admin-dashboard" : "investor-dashboard";

      // Only set default view if current view is still the initial default
      if (state.activeView === 'investor-dashboard') {
        dispatch({ type: 'SET_ACTIVE_VIEW', payload: defaultView });
      }

      refreshData();
    } else if (status === 'unauthenticated') {
      dispatch({ type: 'SET_USER', payload: null });
      dispatch({ type: 'SET_INVESTMENTS', payload: [] });
      dispatch({ type: 'SET_PORTFOLIO_STATS', payload: null });
      dispatch({ type: 'SET_NOTIFICATIONS', payload: [] });
    }
  }, [session, status]);

  const contextValue: DashboardContextType = {
    state,
    dispatch,
    setActiveView,
    toggleSidebar,
    setSidebarOpen,
    markNotificationRead,
    refreshData,
  };

  return (
    <DashboardContext.Provider value={contextValue}>
      {children}
    </DashboardContext.Provider>
  );
}

// Hook
export function useDashboard() {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboard must be used within a DashboardProvider');
  }
  return context;
}
