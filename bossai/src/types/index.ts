// Core Types for AI Venture Platform

export interface SectionProps {
  className?: string;
  children?: React.ReactNode;
}

export interface CardData {
  id: string;
  title: string;
  description: string;
  icon?: React.ReactNode;
  gradient?: string;
  borderColor?: string;
}

export interface FeatureCard extends CardData {
  delay?: number;
}

export interface IndustryCard extends CardData {
  color: string;
}

export interface BenefitCard extends CardData {
  iconBg: string;
}

export interface TechCard extends CardData {
  iconColor: string;
}

// Animation Types
export interface AnimationConfig {
  initial?: object;
  animate?: object;
  transition?: object;
  viewport?: object;
  whileInView?: object;
}

// Button Types
export interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline';
  size?: 'sm' | 'md' | 'lg';
  children: React.ReactNode;
  loading?: boolean;
}

// Theme Types
export interface ThemeColors {
  deepSpace: string;
  electricBlue: string;
  quantumPurple: string;
  cyberGreen: string;
  charcoalBlack: string;
  cloudGray: string;
}

// Component Data Types
export interface HeroData {
  title: string;
  subtitle: string;
  description: string;
  primaryCTA: string;
  secondaryCTA?: string;
}

export interface StoryData {
  title: string;
  paragraphs: string[];
}

export interface WhatWeDoData {
  title: string;
  subtitle: string;
  cards: FeatureCard[];
  bottomText: {
    light: string;
    bold: string;
  };
}

export interface TomorrowData {
  title: string;
  subtitle: string;
  description: string;
  industries: IndustryCard[];
  bottomText: string;
}

export interface PartnershipData {
  title: string;
  subtitle: string;
  benefits: BenefitCard[];
  bottomText: {
    normal: string;
    bold: string;
  };
}

export interface TechTrustData {
  title: string;
  subtitle: string;
  features: TechCard[];
  bottomText: {
    normal: string;
    bold: string;
  };
}

export interface CTAData {
  title: string;
  subtitle: string;
  description: string;
  primaryCTA: string;
  secondaryCTA: string;
}

// Authentication Types
export interface User {
  id: string;
  email: string;
  name?: string;
  role: UserRole;
  image?: string;
}

export interface AuthSession {
  user: User;
  expires: string;
}

// Investment Form Types
export interface InvestmentFormData {
  type: string;
  amount: number;
  riskLevel: string;
  duration?: number;
  description?: string;
  documents?: File[];
}

// Dashboard Types
export interface DashboardStats {
  totalInvestments: number;
  totalValue: number;
  totalReturns: number;
  pendingInvestments: number;
}

// Admin Types
export interface AdminUserData {
  id: string;
  name?: string;
  email: string;
  role: string;
  status: string;
  kycStatus: string;
  createdAt: string;
  lastLoginAt?: string;
}

export interface AdminInvestmentData {
  id: string;
  user: {
    name?: string;
    email: string;
  };
  type: string;
  amount: number;
  status: string;
  submittedAt: string;
}

// Enums (matching Prisma schema)
export enum UserRole {
  USER = "USER",
  INVESTOR = "INVESTOR",
  ADMIN = "ADMIN",
  SUPER_ADMIN = "SUPER_ADMIN"
}
