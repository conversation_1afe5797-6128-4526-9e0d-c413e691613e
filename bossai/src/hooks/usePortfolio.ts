import { useState, useEffect } from 'react';
import { useSession } from 'next-auth/react';

export interface PortfolioStats {
  totalInvestments: number;
  totalValue: number;
  totalReturns: number;
  activeInvestments: number;
  pendingInvestments: number;
  completedInvestments: number;
  averageReturn: number;
  monthlyGrowth: number;
  yearlyGrowth: number;
  totalInvested: number;
  portfolioGrowth: number;
  portfolioGrowthData: Array<{ date: string; value: number }>;
  assetAllocation: Array<{
    category: string;
    percentage: number;
    amount: number;
    count: number;
  }>;
  recentInvestments: Array<{
    id: string;
    type: string;
    amount: number;
    currentValue: number;
    totalReturns: number;
    status: string;
    project?: {
      title: string;
      sector: string;
    };
    submittedAt: Date;
  }>;
  recentTransactions: Array<{
    id: string;
    type: string;
    amount: number;
    status: string;
    createdAt: Date;
    investment: {
      id: string;
      project?: {
        title: string;
      };
    };
  }>;
  performanceMetrics: {
    roi: number;
    volatility: number;
    sharpeRatio: number;
  };
}

export function usePortfolio() {
  const { data: session } = useSession();
  const [stats, setStats] = useState<PortfolioStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchPortfolioStats = async () => {
    if (!session?.user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/portfolio/stats');
      
      if (!response.ok) {
        throw new Error('Failed to fetch portfolio statistics');
      }

      const data = await response.json();
      setStats(data);
    } catch (err) {
      console.error('Error fetching portfolio stats:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch portfolio data');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPortfolioStats();
  }, [session]);

  const refetch = () => {
    fetchPortfolioStats();
  };

  return {
    stats,
    loading,
    error,
    refetch
  };
}

export function useInvestmentOpportunities() {
  const [projects, setProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchProjects = async (filters?: any) => {
    try {
      setLoading(true);
      setError(null);

      const params = new URLSearchParams();
      if (filters?.sector) params.append('sector', filters.sector);
      if (filters?.riskLevel) params.append('riskLevel', filters.riskLevel);
      if (filters?.minInvestment) params.append('minInvestment', filters.minInvestment.toString());
      if (filters?.maxInvestment) params.append('maxInvestment', filters.maxInvestment.toString());

      const response = await fetch(`/api/projects?${params.toString()}`);
      
      if (!response.ok) {
        throw new Error('Failed to fetch investment opportunities');
      }

      const data = await response.json();
      setProjects(data.projects || []);
    } catch (err) {
      console.error('Error fetching projects:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch investment opportunities');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  return {
    projects,
    loading,
    error,
    refetch: fetchProjects
  };
}

export function useFeaturedProjects() {
  const [projects, setProjects] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchFeaturedProjects = async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/projects/featured');
      
      if (!response.ok) {
        throw new Error('Failed to fetch featured projects');
      }

      const data = await response.json();
      setProjects(data.projects || []);
    } catch (err) {
      console.error('Error fetching featured projects:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch featured projects');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchFeaturedProjects();
  }, []);

  return {
    projects,
    loading,
    error,
    refetch: fetchFeaturedProjects
  };
}

export function useDashboardMetrics() {
  const { data: session } = useSession();
  const [metrics, setMetrics] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchMetrics = async () => {
    if (!session?.user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/dashboard/metrics');
      
      if (!response.ok) {
        throw new Error('Failed to fetch dashboard metrics');
      }

      const data = await response.json();
      setMetrics(data);
    } catch (err) {
      console.error('Error fetching dashboard metrics:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch dashboard metrics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchMetrics();
  }, [session]);

  return {
    metrics,
    loading,
    error,
    refetch: fetchMetrics
  };
}

export function useNotifications() {
  const { data: session } = useSession();
  const [notifications, setNotifications] = useState<any[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchNotifications = async () => {
    if (!session?.user) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      setError(null);

      const response = await fetch('/api/notifications');
      
      if (!response.ok) {
        throw new Error('Failed to fetch notifications');
      }

      const data = await response.json();
      setNotifications(data);
      setUnreadCount(data.filter((n: any) => !n.read).length);
    } catch (err) {
      console.error('Error fetching notifications:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch notifications');
    } finally {
      setLoading(false);
    }
  };

  const markAsRead = async (notificationId: string) => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ notificationId })
      });

      if (response.ok) {
        setNotifications(prev => 
          prev.map(n => n.id === notificationId ? { ...n, read: true } : n)
        );
        setUnreadCount(prev => Math.max(0, prev - 1));
      }
    } catch (err) {
      console.error('Error marking notification as read:', err);
    }
  };

  const markAllAsRead = async () => {
    try {
      const response = await fetch('/api/notifications', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ markAllAsRead: true })
      });

      if (response.ok) {
        setNotifications(prev => prev.map(n => ({ ...n, read: true })));
        setUnreadCount(0);
      }
    } catch (err) {
      console.error('Error marking all notifications as read:', err);
    }
  };

  useEffect(() => {
    fetchNotifications();
  }, [session]);

  return {
    notifications,
    unreadCount,
    loading,
    error,
    markAsRead,
    markAllAsRead,
    refetch: fetchNotifications
  };
}
