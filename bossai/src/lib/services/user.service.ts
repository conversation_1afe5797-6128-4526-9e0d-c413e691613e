import { prisma } from '@/lib/prisma';
import { UserRole, UserStatus, KYCStatus } from '@prisma/client';

export interface UserProfile {
  id: string;
  name: string | null;
  email: string;
  role: UserRole;
  status: UserStatus;
  kycStatus: KYCStatus;
  firstName: string | null;
  lastName: string | null;
  phone: string | null;
  dateOfBirth: Date | null;
  address: string | null;
  city: string | null;
  state: string | null;
  zipCode: string | null;
  country: string | null;
  riskTolerance: string | null;
  investmentExperience: string | null;
  annualIncome: string | null;
  netWorth: string | null;
  kycCompletedAt: Date | null;
  lastLoginAt: Date | null;
  createdAt: Date;
  image: string | null;
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  pendingKyc: number;
  approvedKyc: number;
  newUsersThisMonth: number;
  userGrowthRate: number;
}

export interface NotificationSummary {
  id: string;
  title: string;
  message: string;
  type: string;
  priority: string;
  read: boolean;
  actionUrl: string | null;
  createdAt: Date;
}

export class UserService {
  static async getUserProfile(userId: string): Promise<UserProfile | null> {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          status: true,
          kycStatus: true,
          firstName: true,
          lastName: true,
          phone: true,
          dateOfBirth: true,
          address: true,
          city: true,
          state: true,
          zipCode: true,
          country: true,
          riskTolerance: true,
          investmentExperience: true,
          annualIncome: true,
          netWorth: true,
          kycCompletedAt: true,
          lastLoginAt: true,
          createdAt: true,
          image: true
        }
      });

      return user;
    } catch (error) {
      console.error('Error fetching user profile:', error);
      throw new Error('Failed to fetch user profile');
    }
  }

  static async updateUserProfile(userId: string, data: Partial<UserProfile>): Promise<UserProfile> {
    try {
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          ...data,
          updatedAt: new Date()
        },
        select: {
          id: true,
          name: true,
          email: true,
          role: true,
          status: true,
          kycStatus: true,
          firstName: true,
          lastName: true,
          phone: true,
          dateOfBirth: true,
          address: true,
          city: true,
          state: true,
          zipCode: true,
          country: true,
          riskTolerance: true,
          investmentExperience: true,
          annualIncome: true,
          netWorth: true,
          kycCompletedAt: true,
          lastLoginAt: true,
          createdAt: true,
          image: true
        }
      });

      return updatedUser;
    } catch (error) {
      console.error('Error updating user profile:', error);
      throw new Error('Failed to update user profile');
    }
  }

  static async getUserStats(): Promise<UserStats> {
    try {
      const now = new Date();
      const lastMonth = new Date();
      lastMonth.setMonth(lastMonth.getMonth() - 1);

      const [
        totalUsers,
        activeUsers,
        pendingKyc,
        approvedKyc,
        newUsersThisMonth,
        newUsersLastMonth
      ] = await Promise.all([
        prisma.user.count(),
        prisma.user.count({ where: { status: UserStatus.ACTIVE } }),
        prisma.user.count({ where: { kycStatus: KYCStatus.PENDING_REVIEW } }),
        prisma.user.count({ where: { kycStatus: KYCStatus.APPROVED } }),
        prisma.user.count({
          where: {
            createdAt: { gte: lastMonth }
          }
        }),
        prisma.user.count({
          where: {
            createdAt: {
              gte: new Date(lastMonth.getFullYear(), lastMonth.getMonth() - 1, 1),
              lt: lastMonth
            }
          }
        })
      ]);

      const userGrowthRate = newUsersLastMonth > 0 
        ? ((newUsersThisMonth - newUsersLastMonth) / newUsersLastMonth) * 100 
        : 0;

      return {
        totalUsers,
        activeUsers,
        pendingKyc,
        approvedKyc,
        newUsersThisMonth,
        userGrowthRate
      };
    } catch (error) {
      console.error('Error fetching user stats:', error);
      throw new Error('Failed to fetch user statistics');
    }
  }

  static async getUserNotifications(userId: string, limit = 10): Promise<NotificationSummary[]> {
    try {
      const notifications = await prisma.notification.findMany({
        where: { userId },
        orderBy: { createdAt: 'desc' },
        take: limit,
        select: {
          id: true,
          title: true,
          message: true,
          type: true,
          priority: true,
          read: true,
          actionUrl: true,
          createdAt: true
        }
      });

      return notifications.map(notification => ({
        ...notification,
        type: notification.type.toString(),
        priority: notification.priority.toString()
      }));
    } catch (error) {
      console.error('Error fetching user notifications:', error);
      throw new Error('Failed to fetch notifications');
    }
  }

  static async getUnreadNotificationCount(userId: string): Promise<number> {
    try {
      return await prisma.notification.count({
        where: {
          userId,
          read: false
        }
      });
    } catch (error) {
      console.error('Error fetching unread notification count:', error);
      return 0;
    }
  }

  static async markNotificationAsRead(notificationId: string, userId: string): Promise<void> {
    try {
      await prisma.notification.update({
        where: {
          id: notificationId,
          userId // Ensure user can only mark their own notifications
        },
        data: {
          read: true,
          updatedAt: new Date()
        }
      });
    } catch (error) {
      console.error('Error marking notification as read:', error);
      throw new Error('Failed to mark notification as read');
    }
  }

  static async markAllNotificationsAsRead(userId: string): Promise<void> {
    try {
      await prisma.notification.updateMany({
        where: {
          userId,
          read: false
        },
        data: {
          read: true,
          updatedAt: new Date()
        }
      });
    } catch (error) {
      console.error('Error marking all notifications as read:', error);
      throw new Error('Failed to mark all notifications as read');
    }
  }

  static async updateLastLogin(userId: string): Promise<void> {
    try {
      await prisma.user.update({
        where: { id: userId },
        data: {
          lastLoginAt: new Date(),
          updatedAt: new Date()
        }
      });
    } catch (error) {
      console.error('Error updating last login:', error);
      // Don't throw error for this non-critical operation
    }
  }

  static async getUsersByRole(role: UserRole, page = 1, limit = 20) {
    try {
      const skip = (page - 1) * limit;
      
      const [users, total] = await Promise.all([
        prisma.user.findMany({
          where: { role },
          select: {
            id: true,
            name: true,
            email: true,
            status: true,
            kycStatus: true,
            createdAt: true,
            lastLoginAt: true,
            _count: {
              select: {
                investments: true,
                notifications: {
                  where: { read: false }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.user.count({ where: { role } })
      ]);

      return {
        users,
        total,
        pages: Math.ceil(total / limit)
      };
    } catch (error) {
      console.error('Error fetching users by role:', error);
      throw new Error('Failed to fetch users');
    }
  }

  static async searchUsers(query: string, limit = 10) {
    try {
      const users = await prisma.user.findMany({
        where: {
          OR: [
            { name: { contains: query, mode: 'insensitive' } },
            { email: { contains: query, mode: 'insensitive' } },
            { firstName: { contains: query, mode: 'insensitive' } },
            { lastName: { contains: query, mode: 'insensitive' } }
          ]
        },
        select: {
          id: true,
          name: true,
          email: true,
          firstName: true,
          lastName: true,
          role: true,
          status: true,
          kycStatus: true
        },
        take: limit
      });

      return users;
    } catch (error) {
      console.error('Error searching users:', error);
      throw new Error('Failed to search users');
    }
  }
}
