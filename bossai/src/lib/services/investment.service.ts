import { prisma } from '@/lib/prisma';
import { InvestmentStatus, ProjectStatus, RiskLevel, InvestmentType } from '@prisma/client';

export interface ProjectWithStats {
  id: string;
  title: string;
  description: string;
  sector: string;
  fundingGoal: number;
  currentFunding: number;
  fundingProgress: number;
  roiEstimate: number;
  status: ProjectStatus;
  featured: boolean;
  minInvestment: number;
  maxInvestment: number | null;
  riskLevel: RiskLevel;
  timeHorizon: string | null;
  startDate: Date | null;
  endDate: Date | null;
  imageUrl: string | null;
  tags: string | null;
  investorCount: number;
  averageInvestment: number;
  createdAt: Date;
  creator: {
    name: string | null;
    email: string;
  };
}

export interface InvestmentOpportunity {
  id: string;
  title: string;
  sector: string;
  fundingGoal: number;
  currentFunding: number;
  fundingProgress: number;
  roiEstimate: number;
  riskLevel: RiskLevel;
  minInvestment: number;
  timeHorizon: string | null;
  imageUrl: string | null;
  investorCount: number;
}

export interface DashboardMetrics {
  totalProjects: number;
  activeProjects: number;
  totalFunding: number;
  totalInvestors: number;
  averageInvestment: number;
  successRate: number;
  monthlyGrowth: number;
}

export class InvestmentService {
  static async getFeaturedProjects(limit = 6): Promise<InvestmentOpportunity[]> {
    try {
      const projects = await prisma.project.findMany({
        where: {
          status: ProjectStatus.ACTIVE,
          featured: true
        },
        include: {
          investments: {
            where: { status: InvestmentStatus.ACTIVE }
          },
          _count: {
            select: {
              investments: {
                where: { status: InvestmentStatus.ACTIVE }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: limit
      });

      return projects.map(project => {
        const totalInvestment = project.investments.reduce((sum, inv) => sum + Number(inv.amount), 0);
        const fundingProgress = project.fundingGoal > 0 ? (totalInvestment / Number(project.fundingGoal)) * 100 : 0;

        return {
          id: project.id,
          title: project.title,
          sector: project.sector,
          fundingGoal: Number(project.fundingGoal),
          currentFunding: totalInvestment,
          fundingProgress: Math.min(fundingProgress, 100),
          roiEstimate: Number(project.roiEstimate || 0),
          riskLevel: project.riskLevel,
          minInvestment: Number(project.minInvestment),
          timeHorizon: project.timeHorizon,
          imageUrl: project.imageUrl,
          investorCount: project._count.investments
        };
      });
    } catch (error) {
      console.error('Error fetching featured projects:', error);
      throw new Error('Failed to fetch featured projects');
    }
  }

  static async getAllProjects(
    page = 1,
    limit = 12,
    filters?: {
      sector?: string;
      riskLevel?: RiskLevel;
      minInvestment?: number;
      maxInvestment?: number;
    }
  ): Promise<{ projects: ProjectWithStats[]; total: number; pages: number }> {
    try {
      const skip = (page - 1) * limit;
      
      const where: any = {
        status: ProjectStatus.ACTIVE
      };

      if (filters?.sector) {
        where.sector = filters.sector;
      }
      if (filters?.riskLevel) {
        where.riskLevel = filters.riskLevel;
      }
      if (filters?.minInvestment) {
        where.minInvestment = { gte: filters.minInvestment };
      }
      if (filters?.maxInvestment) {
        where.maxInvestment = { lte: filters.maxInvestment };
      }

      const [projects, total] = await Promise.all([
        prisma.project.findMany({
          where,
          include: {
            creator: {
              select: { name: true, email: true }
            },
            investments: {
              where: { status: InvestmentStatus.ACTIVE }
            },
            _count: {
              select: {
                investments: {
                  where: { status: InvestmentStatus.ACTIVE }
                }
              }
            }
          },
          orderBy: { createdAt: 'desc' },
          skip,
          take: limit
        }),
        prisma.project.count({ where })
      ]);

      const projectsWithStats = projects.map(project => {
        const totalInvestment = project.investments.reduce((sum, inv) => sum + Number(inv.amount), 0);
        const fundingProgress = project.fundingGoal > 0 ? (totalInvestment / Number(project.fundingGoal)) * 100 : 0;
        const averageInvestment = project.investments.length > 0 ? totalInvestment / project.investments.length : 0;

        return {
          id: project.id,
          title: project.title,
          description: project.description,
          sector: project.sector,
          fundingGoal: Number(project.fundingGoal),
          currentFunding: totalInvestment,
          fundingProgress: Math.min(fundingProgress, 100),
          roiEstimate: Number(project.roiEstimate || 0),
          status: project.status,
          featured: project.featured,
          minInvestment: Number(project.minInvestment),
          maxInvestment: project.maxInvestment ? Number(project.maxInvestment) : null,
          riskLevel: project.riskLevel,
          timeHorizon: project.timeHorizon,
          startDate: project.startDate,
          endDate: project.endDate,
          imageUrl: project.imageUrl,
          tags: project.tags,
          investorCount: project._count.investments,
          averageInvestment,
          createdAt: project.createdAt,
          creator: project.creator
        };
      });

      return {
        projects: projectsWithStats,
        total,
        pages: Math.ceil(total / limit)
      };
    } catch (error) {
      console.error('Error fetching projects:', error);
      throw new Error('Failed to fetch projects');
    }
  }

  static async getProjectById(id: string): Promise<ProjectWithStats | null> {
    try {
      const project = await prisma.project.findUnique({
        where: { id },
        include: {
          creator: {
            select: { name: true, email: true }
          },
          investments: {
            where: { status: InvestmentStatus.ACTIVE }
          },
          _count: {
            select: {
              investments: {
                where: { status: InvestmentStatus.ACTIVE }
              }
            }
          }
        }
      });

      if (!project) return null;

      const totalInvestment = project.investments.reduce((sum, inv) => sum + Number(inv.amount), 0);
      const fundingProgress = project.fundingGoal > 0 ? (totalInvestment / Number(project.fundingGoal)) * 100 : 0;
      const averageInvestment = project.investments.length > 0 ? totalInvestment / project.investments.length : 0;

      return {
        id: project.id,
        title: project.title,
        description: project.description,
        sector: project.sector,
        fundingGoal: Number(project.fundingGoal),
        currentFunding: totalInvestment,
        fundingProgress: Math.min(fundingProgress, 100),
        roiEstimate: Number(project.roiEstimate || 0),
        status: project.status,
        featured: project.featured,
        minInvestment: Number(project.minInvestment),
        maxInvestment: project.maxInvestment ? Number(project.maxInvestment) : null,
        riskLevel: project.riskLevel,
        timeHorizon: project.timeHorizon,
        startDate: project.startDate,
        endDate: project.endDate,
        imageUrl: project.imageUrl,
        tags: project.tags,
        investorCount: project._count.investments,
        averageInvestment,
        createdAt: project.createdAt,
        creator: project.creator
      };
    } catch (error) {
      console.error('Error fetching project by ID:', error);
      throw new Error('Failed to fetch project');
    }
  }

  static async getDashboardMetrics(): Promise<DashboardMetrics> {
    try {
      const [
        totalProjects,
        activeProjects,
        totalFundingResult,
        totalInvestorsResult,
        completedProjects
      ] = await Promise.all([
        prisma.project.count(),
        prisma.project.count({ where: { status: ProjectStatus.ACTIVE } }),
        prisma.investment.aggregate({
          where: { status: InvestmentStatus.ACTIVE },
          _sum: { amount: true }
        }),
        prisma.investment.groupBy({
          by: ['userId'],
          where: { status: InvestmentStatus.ACTIVE }
        }),
        prisma.project.count({ where: { status: ProjectStatus.COMPLETED } })
      ]);

      const totalFunding = Number(totalFundingResult._sum.amount || 0);
      const totalInvestors = totalInvestorsResult.length;
      const averageInvestment = totalInvestors > 0 ? totalFunding / totalInvestors : 0;
      const successRate = totalProjects > 0 ? (completedProjects / totalProjects) * 100 : 0;

      // Calculate monthly growth (simplified)
      const monthlyGrowth = await this.calculateMonthlyGrowth();

      return {
        totalProjects,
        activeProjects,
        totalFunding,
        totalInvestors,
        averageInvestment,
        successRate,
        monthlyGrowth
      };
    } catch (error) {
      console.error('Error fetching dashboard metrics:', error);
      throw new Error('Failed to fetch dashboard metrics');
    }
  }

  private static async calculateMonthlyGrowth(): Promise<number> {
    try {
      const now = new Date();
      const lastMonth = new Date();
      lastMonth.setMonth(lastMonth.getMonth() - 1);

      const [currentMonth, previousMonth] = await Promise.all([
        prisma.investment.aggregate({
          where: {
            createdAt: { gte: lastMonth },
            status: InvestmentStatus.ACTIVE
          },
          _sum: { amount: true }
        }),
        prisma.investment.aggregate({
          where: {
            createdAt: { 
              gte: new Date(lastMonth.getFullYear(), lastMonth.getMonth() - 1, 1),
              lt: lastMonth
            },
            status: InvestmentStatus.ACTIVE
          },
          _sum: { amount: true }
        })
      ]);

      const current = Number(currentMonth._sum.amount || 0);
      const previous = Number(previousMonth._sum.amount || 0);

      return previous > 0 ? ((current - previous) / previous) * 100 : 0;
    } catch (error) {
      console.error('Error calculating monthly growth:', error);
      return 0;
    }
  }

  static async getSectorDistribution() {
    try {
      const projects = await prisma.project.groupBy({
        by: ['sector'],
        where: { status: ProjectStatus.ACTIVE },
        _count: { _all: true },
        _sum: { currentFunding: true }
      });

      return projects.map(project => ({
        sector: project.sector,
        count: project._count._all,
        funding: Number(project._sum.currentFunding || 0)
      }));
    } catch (error) {
      console.error('Error fetching sector distribution:', error);
      throw new Error('Failed to fetch sector distribution');
    }
  }
}
