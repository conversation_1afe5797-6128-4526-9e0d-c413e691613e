import { prisma } from '@/lib/prisma';
import { InvestmentStatus, TransactionType, TransactionStatus } from '@prisma/client';

export interface PortfolioStats {
  totalInvestments: number;
  totalValue: number;
  totalReturns: number;
  activeInvestments: number;
  pendingInvestments: number;
  completedInvestments: number;
  averageReturn: number;
  monthlyGrowth: number;
  yearlyGrowth: number;
}

export interface InvestmentSummary {
  id: string;
  type: string;
  amount: number;
  currentValue: number;
  totalReturns: number;
  status: InvestmentStatus;
  project?: {
    title: string;
    sector: string;
  };
  submittedAt: Date;
}

export interface TransactionSummary {
  id: string;
  type: TransactionType;
  amount: number;
  status: TransactionStatus;
  createdAt: Date;
  investment: {
    id: string;
    project?: {
      title: string;
    };
  };
}

export class PortfolioService {
  static async getPortfolioStats(userId: string): Promise<PortfolioStats> {
    try {
      // Get all user investments with aggregations
      const investments = await prisma.investment.findMany({
        where: { userId },
        include: {
          project: {
            select: { title: true, sector: true }
          },
          transactions: {
            where: { status: TransactionStatus.COMPLETED }
          }
        }
      });

      // Calculate basic stats
      const totalInvestments = investments.length;
      const totalValue = investments.reduce((sum, inv) => sum + (inv.currentValue || inv.amount), 0);
      const totalReturns = investments.reduce((sum, inv) => sum + (inv.totalReturns || 0), 0);
      
      // Status counts
      const activeInvestments = investments.filter(inv => inv.status === InvestmentStatus.ACTIVE).length;
      const pendingInvestments = investments.filter(inv => inv.status === InvestmentStatus.PENDING).length;
      const completedInvestments = investments.filter(inv => inv.status === InvestmentStatus.COMPLETED).length;

      // Calculate average return
      const averageReturn = totalInvestments > 0 ? totalReturns / totalInvestments : 0;

      // Calculate growth rates (simplified - in production, use proper time-series analysis)
      const monthlyGrowth = await this.calculateGrowthRate(userId, 30);
      const yearlyGrowth = await this.calculateGrowthRate(userId, 365);

      return {
        totalInvestments,
        totalValue,
        totalReturns,
        activeInvestments,
        pendingInvestments,
        completedInvestments,
        averageReturn,
        monthlyGrowth,
        yearlyGrowth
      };
    } catch (error) {
      console.error('Error fetching portfolio stats:', error);
      throw new Error('Failed to fetch portfolio statistics');
    }
  }

  static async getInvestmentSummary(userId: string, limit = 10): Promise<InvestmentSummary[]> {
    try {
      const investments = await prisma.investment.findMany({
        where: { userId },
        include: {
          project: {
            select: { title: true, sector: true }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: limit
      });

      return investments.map(inv => ({
        id: inv.id,
        type: inv.type,
        amount: Number(inv.amount),
        currentValue: Number(inv.currentValue || inv.amount),
        totalReturns: Number(inv.totalReturns || 0),
        status: inv.status,
        project: inv.project ? {
          title: inv.project.title,
          sector: inv.project.sector
        } : undefined,
        submittedAt: inv.submittedAt
      }));
    } catch (error) {
      console.error('Error fetching investment summary:', error);
      throw new Error('Failed to fetch investment summary');
    }
  }

  static async getRecentTransactions(userId: string, limit = 10): Promise<TransactionSummary[]> {
    try {
      const transactions = await prisma.transaction.findMany({
        where: {
          investment: { userId }
        },
        include: {
          investment: {
            include: {
              project: {
                select: { title: true }
              }
            }
          }
        },
        orderBy: { createdAt: 'desc' },
        take: limit
      });

      return transactions.map(tx => ({
        id: tx.id,
        type: tx.type,
        amount: Number(tx.amount),
        status: tx.status,
        createdAt: tx.createdAt,
        investment: {
          id: tx.investment.id,
          project: tx.investment.project ? {
            title: tx.investment.project.title
          } : undefined
        }
      }));
    } catch (error) {
      console.error('Error fetching recent transactions:', error);
      throw new Error('Failed to fetch recent transactions');
    }
  }

  private static async calculateGrowthRate(userId: string, days: number): Promise<number> {
    try {
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      // Get investments created in the period
      const periodInvestments = await prisma.investment.findMany({
        where: {
          userId,
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        }
      });

      // Calculate growth based on returns in the period
      const periodReturns = periodInvestments.reduce((sum, inv) => sum + (inv.totalReturns || 0), 0);
      const periodInvestment = periodInvestments.reduce((sum, inv) => sum + inv.amount, 0);

      return periodInvestment > 0 ? (periodReturns / periodInvestment) * 100 : 0;
    } catch (error) {
      console.error('Error calculating growth rate:', error);
      return 0;
    }
  }

  static async getPortfolioDistribution(userId: string) {
    try {
      const investments = await prisma.investment.findMany({
        where: { userId },
        include: {
          project: {
            select: { sector: true }
          }
        }
      });

      // Group by sector
      const distribution = investments.reduce((acc, inv) => {
        const sector = inv.project?.sector || 'Other';
        if (!acc[sector]) {
          acc[sector] = { value: 0, count: 0 };
        }
        acc[sector].value += Number(inv.currentValue || inv.amount);
        acc[sector].count += 1;
        return acc;
      }, {} as Record<string, { value: number; count: number }>);

      return Object.entries(distribution).map(([sector, data]) => ({
        sector,
        value: data.value,
        count: data.count,
        percentage: 0 // Will be calculated on frontend
      }));
    } catch (error) {
      console.error('Error fetching portfolio distribution:', error);
      throw new Error('Failed to fetch portfolio distribution');
    }
  }
}
