{"name": "<PERSON>ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "prisma generate && next build --turbopack", "start": "next start", "lint": "eslint", "postinstall": "prisma generate", "db:generate": "prisma generate", "db:push": "prisma db push", "db:seed": "tsx prisma/seed.ts", "db:studio": "prisma studio", "heroku-postbuild": "npm run build"}, "dependencies": {"@aws-sdk/client-s3": "^3.884.0", "@heroicons/react": "^2.2.0", "@hookform/resolvers": "^5.2.1", "@next-auth/prisma-adapter": "^1.0.7", "@prisma/client": "^6.15.0", "@radix-ui/react-icons": "^1.3.2", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.13", "@stripe/stripe-js": "^7.9.0", "@types/bcryptjs": "^2.4.6", "@types/jsonwebtoken": "^9.0.10", "@types/multer": "^2.0.0", "@types/nodemailer": "^7.0.1", "aws-sdk": "^2.1692.0", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "framer-motion": "^12.23.12", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.542.0", "multer": "^2.0.2", "next": "15.5.2", "next-auth": "^4.24.11", "next-themes": "^0.4.6", "nodemailer": "^6.10.1", "prisma": "^6.15.0", "react": "19.1.0", "react-dom": "19.1.0", "react-hook-form": "^7.62.0", "stripe": "^18.5.0", "tailwind-merge": "^3.3.1", "zod": "^4.1.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.2", "tailwindcss": "^4", "tsx": "^4.20.5", "tw-animate-css": "^1.3.8", "typescript": "^5"}, "prisma": {"seed": "tsx prisma/seed.ts"}}