import { PrismaClient } from '@prisma/client';
import bcrypt from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Seeding database...');

  // Create default admin user
  const adminEmail = '<EMAIL>';
  const adminPassword = 'BossAI2024!';

  // Check if admin user already exists
  const existingAdmin = await prisma.user.findUnique({
    where: { email: adminEmail },
  });

  if (!existingAdmin) {
    const hashedPassword = await bcrypt.hash(adminPassword, 12);

    const adminUser = await prisma.user.create({
      data: {
        email: adminEmail,
        name: 'Boss AI Admin',
        firstName: 'Boss AI',
        lastName: 'Admin',
        password: hashedPassword,
        role: 'SUPER_ADMIN',
        kycStatus: 'APPROVED',
        emailVerified: new Date(),
      },
    });

    console.log('✅ Created admin user:', {
      email: adminUser.email,
      role: adminUser.role,
      id: adminUser.id,
    });

    // Create audit log for admin creation
    await prisma.auditLog.create({
      data: {
        userId: adminUser.id,
        action: 'ADMIN_USER_CREATED',
        entity: 'User',
        entityId: adminUser.id,
        newValues: {
          email: adminUser.email,
          role: adminUser.role,
          createdBy: 'system',
          timestamp: new Date().toISOString(),
        },
        ipAddress: 'system',
        userAgent: 'seed-script',
      },
    });

    console.log('✅ Created audit log for admin user');
  } else {
    console.log('ℹ️  Admin user already exists:', existingAdmin.email);
  }

  // Create test investor user
  const investorEmail = '<EMAIL>';
  const investorPassword = 'Investor2024!';

  const existingInvestor = await prisma.user.findUnique({
    where: { email: investorEmail },
  });

  if (!existingInvestor) {
    const hashedPassword = await bcrypt.hash(investorPassword, 12);

    const investorUser = await prisma.user.create({
      data: {
        email: investorEmail,
        name: 'Test Investor',
        firstName: 'Test',
        lastName: 'Investor',
        password: hashedPassword,
        role: 'INVESTOR',
        kycStatus: 'PENDING_REVIEW',
        emailVerified: new Date(),
      },
    });

    console.log('✅ Created investor user:', {
      email: investorUser.email,
      role: investorUser.role,
      id: investorUser.id,
    });

    // Create a sample investment for the investor
    await prisma.investment.create({
      data: {
        userId: investorUser.id,
        type: 'EQUITY',
        amount: 50000,
        currency: 'USD',
        riskLevel: 'MODERATE',
        description: 'Sample AI startup investment',
        status: 'PENDING',
        expectedReturn: 15,
        duration: 36,
      },
    });

    console.log('✅ Created sample investment for investor');
  } else {
    console.log('ℹ️  Investor user already exists:', existingInvestor.email);
  }

  console.log('🎉 Database seeding completed!');
  console.log('\n📋 Test Credentials:');
  console.log('Admin Login:');
  console.log('  Email: <EMAIL>');
  console.log('  Password: BossAI2024!');
  console.log('\nInvestor Login:');
  console.log('  Email: <EMAIL>');
  console.log('  Password: Investor2024!');
  console.log('\n🚀 You can now sign in at: http://localhost:3000/auth/signin');
}

main()
  .catch((e) => {
    console.error('❌ Error seeding database:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
