# Boss AI Platform - Heroku Deployment Guide

## 🚀 Quick Deployment Steps

### Prerequisites
- Heroku CLI installed
- GitHub repository: https://github.com/joelgriiyo/bossai.git
- Heroku account

### 1. Create Heroku App
```bash
heroku create your-app-name
# or use the Heroku dashboard
```

### 2. Add PostgreSQL Database
```bash
heroku addons:create heroku-postgresql:essential-0
```

### 3. Set Environment Variables
```bash
# Required for production
heroku config:set NEXTAUTH_URL=https://your-app-name.herokuapp.com
heroku config:set NEXTAUTH_SECRET=$(openssl rand -base64 32)
heroku config:set NODE_ENV=production

# Database (automatically set by Heroku PostgreSQL addon)
# DATABASE_URL will be automatically configured

# Google OAuth (required for authentication)
heroku config:set GOOGLE_CLIENT_ID=your-google-client-id
heroku config:set GOOGLE_CLIENT_SECRET=your-google-client-secret

# Email Configuration
heroku config:set EMAIL_SERVER_HOST=smtp.gmail.com
heroku config:set EMAIL_SERVER_PORT=587
heroku config:set EMAIL_SERVER_USER=<EMAIL>
heroku config:set EMAIL_SERVER_PASSWORD=your-app-password
heroku config:set EMAIL_FROM=<EMAIL>

# AWS S3 (for file storage)
heroku config:set AWS_ACCESS_KEY_ID=your-aws-access-key
heroku config:set AWS_SECRET_ACCESS_KEY=your-aws-secret-key
heroku config:set AWS_REGION=us-east-1
heroku config:set AWS_S3_BUCKET=your-s3-bucket-name

# Security
heroku config:set ENCRYPTION_KEY=$(openssl rand -base64 32)
heroku config:set JWT_SECRET=$(openssl rand -base64 32)

# Optional: Stripe for payments
heroku config:set STRIPE_PUBLISHABLE_KEY=pk_live_your-key
heroku config:set STRIPE_SECRET_KEY=sk_live_your-key
heroku config:set STRIPE_WEBHOOK_SECRET=whsec_your-secret
```

### 4. Deploy from GitHub
```bash
# Connect to GitHub repository
heroku git:remote -a your-app-name

# Deploy
git push heroku main
```

### 5. Run Database Migrations
```bash
heroku run npm run db:push
heroku run npm run db:seed
```

## 🔧 Configuration Details

### Required Environment Variables
- `NEXTAUTH_URL`: Your Heroku app URL
- `NEXTAUTH_SECRET`: Random secret for NextAuth
- `DATABASE_URL`: PostgreSQL connection (auto-configured)
- `GOOGLE_CLIENT_ID` & `GOOGLE_CLIENT_SECRET`: OAuth credentials

### Google OAuth Setup
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create/select project
3. Enable Google+ API
4. Create OAuth 2.0 credentials
5. Add authorized redirect URI: `https://your-app-name.herokuapp.com/api/auth/callback/google`

### AWS S3 Setup (for file uploads)
1. Create S3 bucket
2. Create IAM user with S3 permissions
3. Get access keys
4. Configure CORS for your domain

## 📱 Post-Deployment

### 1. Verify Deployment
- Visit: `https://your-app-name.herokuapp.com`
- Test authentication
- Check database connectivity

### 2. Create Admin User
The seed script creates a default admin:
- Email: <EMAIL>
- Password: BossAI2024!
- Role: SUPER_ADMIN

### 3. Configure Domain (Optional)
```bash
heroku domains:add yourdomain.com
# Follow Heroku's custom domain setup guide
```

## 🔍 Troubleshooting

### Common Issues
1. **Build Failures**: Check build logs with `heroku logs --tail`
2. **Database Issues**: Ensure DATABASE_URL is set correctly
3. **Authentication Issues**: Verify NEXTAUTH_URL and Google OAuth settings

### Useful Commands
```bash
# View logs
heroku logs --tail

# Run database commands
heroku run npm run db:push
heroku run npm run db:studio

# Restart app
heroku restart

# Check config
heroku config
```

## 🔒 Security Checklist
- [ ] All secrets are properly set in Heroku config
- [ ] Google OAuth redirect URIs are configured for production
- [ ] Database is using SSL (Heroku PostgreSQL default)
- [ ] File uploads are secured with proper S3 permissions
- [ ] Rate limiting is configured

## 📊 Monitoring
- Use Heroku metrics dashboard
- Set up log drains for external monitoring
- Configure alerts for errors and performance

---

**Repository**: https://github.com/joelgriiyo/bossai.git
**Status**: ✅ Ready for deployment
