<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Where Vision Meets Intelligence</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');
        
        * {
            font-family: 'Inter', sans-serif;
        }
        
        .gradient-bg {
            background: linear-gradient(135deg, #0f0f23 0%, #1a1a2e 50%, #16213e 100%);
        }
        
        .ai-pattern {
            background-image: 
                radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 75% 75%, rgba(147, 51, 234, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 50% 50%, rgba(34, 197, 94, 0.05) 0%, transparent 50%);
        }
        
        .glow-text {
            text-shadow: 0 0 20px rgba(59, 130, 246, 0.3);
        }
        
        .floating-orb {
            position: absolute;
            border-radius: 50%;
            filter: blur(1px);
            animation: float 6s ease-in-out infinite;
        }
        
        .floating-orb:nth-child(1) {
            width: 100px;
            height: 100px;
            background: linear-gradient(45deg, rgba(59, 130, 246, 0.3), rgba(147, 51, 234, 0.3));
            top: 20%;
            left: 10%;
            animation-delay: 0s;
        }
        
        .floating-orb:nth-child(2) {
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, rgba(34, 197, 94, 0.3), rgba(59, 130, 246, 0.3));
            top: 60%;
            right: 15%;
            animation-delay: 2s;
        }
        
        .floating-orb:nth-child(3) {
            width: 80px;
            height: 80px;
            background: linear-gradient(45deg, rgba(147, 51, 234, 0.3), rgba(34, 197, 94, 0.3));
            bottom: 30%;
            left: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            33% { transform: translateY(-20px) rotate(120deg); }
            66% { transform: translateY(10px) rotate(240deg); }
        }
        
        .tech-grid {
            background-image: 
                linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
                linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
            background-size: 50px 50px;
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(59, 130, 246, 0.2);
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #3b82f6, #8b5cf6);
            transition: all 0.3s ease;
        }
        
        .btn-primary:hover {
            background: linear-gradient(135deg, #2563eb, #7c3aed);
            transform: translateY(-2px);
            box-shadow: 0 10px 30px rgba(59, 130, 246, 0.4);
        }
        
        .btn-secondary {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.1), rgba(147, 51, 234, 0.1));
            border: 1px solid rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
        }
        
        .btn-secondary:hover {
            background: linear-gradient(135deg, rgba(59, 130, 246, 0.2), rgba(147, 51, 234, 0.2));
            border-color: rgba(59, 130, 246, 0.5);
            transform: translateY(-2px);
        }
        
        .neural-network {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0.1;
            background-image: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%233b82f6' fill-opacity='0.3'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3Ccircle cx='10' cy='10' r='1'/%3E%3Ccircle cx='50' cy='10' r='1'/%3E%3Ccircle cx='10' cy='50' r='1'/%3E%3Ccircle cx='50' cy='50' r='1'/%3E%3Cline x1='30' y1='30' x2='10' y2='10' stroke='%233b82f6' stroke-width='0.5'/%3E%3Cline x1='30' y1='30' x2='50' y2='10' stroke='%233b82f6' stroke-width='0.5'/%3E%3Cline x1='30' y1='30' x2='10' y2='50' stroke='%233b82f6' stroke-width='0.5'/%3E%3Cline x1='30' y1='30' x2='50' y2='50' stroke='%233b82f6' stroke-width='0.5'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        }
    </style>
</head>
<body class="gradient-bg text-white overflow-x-hidden">
    <!-- Hero Section -->
    <section class="relative min-h-screen flex items-center justify-center ai-pattern">
        <div class="floating-orb"></div>
        <div class="floating-orb"></div>
        <div class="floating-orb"></div>
        
        <div class="container mx-auto px-6 text-center relative z-10">
            <h1 class="text-6xl md:text-8xl font-black mb-8 glow-text leading-tight">
                Where Vision<br>
                <span class="bg-gradient-to-r from-blue-400 via-purple-500 to-green-400 bg-clip-text text-transparent">
                    Meets Intelligence
                </span>
            </h1>
            
            <p class="text-xl md:text-2xl text-gray-300 max-w-4xl mx-auto mb-12 leading-relaxed font-light">
                We don't just create AI — we forge alliances that transform strong products into market leaders. Through carefully chosen Joint Ventures, we bring the intelligence, you bring the spark, and together we ignite growth that lasts.
            </p>
            
            <button class="btn-primary text-white px-12 py-4 rounded-full text-xl font-semibold inline-flex items-center gap-3">
                👉 Let's Build Together
            </button>
        </div>
    </section>

    <!-- Our Story -->
    <section class="py-24 relative">
        <div class="neural-network"></div>
        <div class="container mx-auto px-6">
            <div class="grid md:grid-cols-2 gap-16 items-center">
                <div class="relative">
                    <div class="w-full h-96 bg-gradient-to-br from-blue-600/20 to-purple-600/20 rounded-3xl flex items-center justify-center relative overflow-hidden">
                        <svg class="w-64 h-64 text-blue-400 opacity-60" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z"/>
                        </svg>
                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/5 to-transparent transform -skew-x-12 animate-pulse"></div>
                    </div>
                </div>
                
                <div>
                    <h2 class="text-5xl font-bold mb-8 bg-gradient-to-r from-white to-gray-300 bg-clip-text text-transparent">
                        Our Story
                    </h2>
                    <div class="space-y-6 text-lg text-gray-300 leading-relaxed">
                        <p>The world is overflowing with AI companies chasing the next buzzword. <strong class="text-white">That's not us.</strong></p>
                        
                        <p>We believe the future belongs to those who already have something real — a product, a service, a solution that's working — and just need the right catalyst to scale. <strong class="text-blue-400">That catalyst is our intelligence.</strong></p>
                        
                        <p>We exist to merge proven ideas with breakthrough AI, not as vendors, but as <strong class="text-purple-400">true partners in creation.</strong></p>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- What We Do Today -->
    <section class="py-24 bg-black/20">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-5xl font-bold mb-6">What We Do Today</h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Right now, our energy is dedicated to Joint Ventures with companies ready for their next leap forward.
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8">
                <div class="card-hover bg-gradient-to-br from-blue-900/30 to-purple-900/30 p-8 rounded-2xl border border-blue-500/20">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Identify</h3>
                    <p class="text-gray-300">Products with untapped growth potential.</p>
                </div>
                
                <div class="card-hover bg-gradient-to-br from-purple-900/30 to-green-900/30 p-8 rounded-2xl border border-purple-500/20">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-green-500 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Embed</h3>
                    <p class="text-gray-300">Our AI to enhance performance, reach, and efficiency.</p>
                </div>
                
                <div class="card-hover bg-gradient-to-br from-green-900/30 to-blue-900/30 p-8 rounded-2xl border border-green-500/20">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-blue-500 rounded-xl flex items-center justify-center mb-6">
                        <svg class="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-4">Share</h3>
                    <p class="text-gray-300">The journey — and the rewards — as equal partners.</p>
                </div>
            </div>
            
            <div class="text-center mt-16">
                <p class="text-2xl font-light text-gray-300 mb-4">This isn't a service.</p>
                <p class="text-3xl font-bold bg-gradient-to-r from-blue-400 to-purple-400 bg-clip-text text-transparent">
                    It's a partnership designed to multiply possibility.
                </p>
            </div>
        </div>
    </section>

    <!-- Tomorrow -->
    <section class="py-24 relative tech-grid">
        <div class="absolute inset-0 bg-gradient-to-r from-blue-900/20 via-purple-900/20 to-green-900/20"></div>
        <div class="container mx-auto px-6 relative z-10">
            <div class="text-center mb-16">
                <h2 class="text-6xl font-bold mb-8 glow-text">Tomorrow</h2>
                <p class="text-2xl text-gray-300 max-w-2xl mx-auto mb-12">
                    Our horizon is much bigger.
                </p>
                <p class="text-xl text-gray-400 max-w-4xl mx-auto">
                    The ventures we form today are the foundation for a future where AI reshapes industries:
                </p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-6 mb-16">
                <div class="bg-gradient-to-br from-yellow-900/30 to-orange-900/30 p-6 rounded-xl border border-yellow-500/20">
                    <h3 class="text-xl font-bold text-yellow-400 mb-2">Energy</h3>
                    <p class="text-gray-300">that runs smarter.</p>
                </div>
                
                <div class="bg-gradient-to-br from-red-900/30 to-pink-900/30 p-6 rounded-xl border border-red-500/20">
                    <h3 class="text-xl font-bold text-red-400 mb-2">Healthcare</h3>
                    <p class="text-gray-300">that predicts, not just treats.</p>
                </div>
                
                <div class="bg-gradient-to-br from-green-900/30 to-emerald-900/30 p-6 rounded-xl border border-green-500/20">
                    <h3 class="text-xl font-bold text-green-400 mb-2">Finance</h3>
                    <p class="text-gray-300">that protects and empowers.</p>
                </div>
                
                <div class="bg-gradient-to-br from-blue-900/30 to-cyan-900/30 p-6 rounded-xl border border-blue-500/20">
                    <h3 class="text-xl font-bold text-blue-400 mb-2">Agriculture</h3>
                    <p class="text-gray-300">that sustains communities.</p>
                </div>
                
                <div class="bg-gradient-to-br from-purple-900/30 to-indigo-900/30 p-6 rounded-xl border border-purple-500/20">
                    <h3 class="text-xl font-bold text-purple-400 mb-2">Entertainment</h3>
                    <p class="text-gray-300">that connects us more deeply.</p>
                </div>
            </div>
            
            <div class="text-center">
                <p class="text-2xl font-light text-gray-300">
                    We're not waiting for the future. We're building it, step by step, partnership by partnership.
                </p>
            </div>
        </div>
    </section>

    <!-- Why Partner With Us -->
    <section class="py-24 bg-black/30">
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-5xl font-bold mb-8">Why Partner With Us</h2>
                <p class="text-xl text-gray-300">Because we believe in:</p>
            </div>
            
            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-3 text-blue-400">Shared Growth</h3>
                    <p class="text-gray-300">We only win when you win.</p>
                </div>
                
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-purple-500 to-green-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-3 text-purple-400">True Collaboration</h3>
                    <p class="text-gray-300">Not client and vendor, but equals.</p>
                </div>
                
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-green-500 to-yellow-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-3 text-green-400">Vision with Action</h3>
                    <p class="text-gray-300">Big dreams, grounded in execution.</p>
                </div>
                
                <div class="text-center">
                    <div class="w-20 h-20 bg-gradient-to-br from-yellow-500 to-red-500 rounded-full flex items-center justify-center mx-auto mb-6">
                        <svg class="w-10 h-10 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-2xl font-bold mb-3 text-yellow-400">Impact</h3>
                    <p class="text-gray-300">Making products unforgettable.</p>
                </div>
            </div>
            
            <div class="text-center">
                <p class="text-2xl font-light text-gray-300">
                    Your product already matters. <span class="text-white font-semibold">With our AI, it becomes unforgettable.</span>
                </p>
            </div>
        </div>
    </section>

    <!-- Tech + Trust -->
    <section class="py-24 relative">
        <div class="neural-network"></div>
        <div class="container mx-auto px-6">
            <div class="text-center mb-16">
                <h2 class="text-5xl font-bold mb-8">Tech + Trust</h2>
                <p class="text-xl text-gray-300 max-w-3xl mx-auto">
                    Behind the vision is a platform built for scale, security, and precision:
                </p>
            </div>
            
            <div class="grid md:grid-cols-3 gap-8 mb-12">
                <div class="bg-gradient-to-br from-gray-900/50 to-blue-900/30 p-8 rounded-2xl border border-gray-700/50">
                    <div class="w-12 h-12 bg-blue-500 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Enterprise Protection</h3>
                    <p class="text-gray-300">Enterprise-level protection for sensitive data.</p>
                </div>
                
                <div class="bg-gradient-to-br from-gray-900/50 to-purple-900/30 p-8 rounded-2xl border border-gray-700/50">
                    <div class="w-12 h-12 bg-purple-500 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Scalable Infrastructure</h3>
                    <p class="text-gray-300">Infrastructure designed to grow with you.</p>
                </div>
                
                <div class="bg-gradient-to-br from-gray-900/50 to-green-900/30 p-8 rounded-2xl border border-gray-700/50">
                    <div class="w-12 h-12 bg-green-500 rounded-lg flex items-center justify-center mb-6">
                        <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold mb-3">Custom Solutions</h3>
                    <p class="text-gray-300">Custom AI solutions, engineered for your unique challenges.</p>
                </div>
            </div>
            
            <div class="text-center">
                <p class="text-2xl font-light text-gray-300">
                    We don't experiment on you. <span class="text-white font-semibold">We engineer with you.</span>
                </p>
            </div>
        </div>
    </section>

    <!-- Closing CTA -->
    <section class="py-32 relative ai-pattern">
        <div class="floating-orb"></div>
        <div class="floating-orb"></div>
        <div class="floating-orb"></div>
        
        <div class="container mx-auto px-6 text-center relative z-10">
            <h2 class="text-5xl md:text-7xl font-black mb-8 glow-text leading-tight">
                "The future doesn't belong<br>
                to those who wait.<br>
                <span class="bg-gradient-to-r from-blue-400 via-purple-500 to-green-400 bg-clip-text text-transparent">
                    It belongs to those who build."
                </span>
            </h2>
            
            <p class="text-2xl text-gray-300 mb-8">Join us, not as clients, but as partners.</p>
            <p class="text-xl text-gray-400 mb-16">Together, we'll turn intelligence into impact.</p>
            
            <div class="flex flex-col sm:flex-row gap-6 justify-center items-center">
                <button class="btn-primary text-white px-10 py-4 rounded-full text-lg font-semibold inline-flex items-center gap-3">
                    👉 Start a Conversation
                </button>
                <button class="btn-secondary text-white px-10 py-4 rounded-full text-lg font-semibold">
                    Explore Partnerships
                </button>
            </div>
        </div>
    </section>
<script>(function(){function c(){var b=a.contentDocument||a.contentWindow.document;if(b){var d=b.createElement('script');d.innerHTML="window.__CF$cv$params={r:'97c04905447fa04f',t:'MTc1NzM1Mzc5NS4wMDAwMDA='};var a=document.createElement('script');a.nonce='';a.src='/cdn-cgi/challenge-platform/scripts/jsd/main.js';document.getElementsByTagName('head')[0].appendChild(a);";b.getElementsByTagName('head')[0].appendChild(d)}}if(document.body){var a=document.createElement('iframe');a.height=1;a.width=1;a.style.position='absolute';a.style.top=0;a.style.left=0;a.style.border='none';a.style.visibility='hidden';document.body.appendChild(a);if('loading'!==document.readyState)c();else if(window.addEventListener)document.addEventListener('DOMContentLoaded',c);else{var e=document.onreadystatechange||function(){};document.onreadystatechange=function(b){e(b);'loading'!==document.readyState&&(document.onreadystatechange=e,c())}}}})();</script></body>
</html>
